import api from './api'

export interface Farm {
  id: number
  name: string
  location: string
  address: string
  coordinates: {
    latitude: number
    longitude: number
  }
  size: number
  sizeUnit: 'acres' | 'hectares'
  fields: number
  crops: string[]
  status: 'Active' | 'Inactive' | 'Seasonal'
  farmType: 'Crop' | 'Livestock' | 'Mixed' | 'Organic' | 'Dairy'
  soilType: string
  climate: string
  waterSource: string[]
  irrigation: boolean
  certifications: string[]
  owner: {
    name: string
    email: string
    phone: string
  }
  manager: {
    name: string
    email: string
    phone: string
  }
  established: string
  totalRevenue: number
  totalExpenses: number
  employees: number
  equipment: string[]
  buildings: {
    type: string
    count: number
  }[]
  insurance: {
    provider: string
    policyNumber: string
    expiryDate: string
  }
  notes: string
  createdAt: string
  updatedAt: string
}

export interface CreateFarmData {
  name: string
  location: string
  address: string
  coordinates: {
    latitude: number
    longitude: number
  }
  size: number
  sizeUnit: 'acres' | 'hectares'
  farmType: 'Crop' | 'Livestock' | 'Mixed' | 'Organic' | 'Dairy'
  soilType: string
  climate: string
  waterSource: string[]
  irrigation: boolean
  certifications: string[]
  owner: {
    name: string
    email: string
    phone: string
  }
  manager: {
    name: string
    email: string
    phone: string
  }
  established: string
  employees: number
  equipment: string[]
  buildings: {
    type: string
    count: number
  }[]
  insurance: {
    provider: string
    policyNumber: string
    expiryDate: string
  }
  notes: string
}

export interface UpdateFarmData {
  name?: string
  location?: string
  address?: string
  coordinates?: {
    latitude: number
    longitude: number
  }
  size?: number
  sizeUnit?: 'acres' | 'hectares'
  farmType?: 'Crop' | 'Livestock' | 'Mixed' | 'Organic' | 'Dairy'
  soilType?: string
  climate?: string
  waterSource?: string[]
  irrigation?: boolean
  certifications?: string[]
  owner?: {
    name: string
    email: string
    phone: string
  }
  manager?: {
    name: string
    email: string
    phone: string
  }
  established?: string
  employees?: number
  equipment?: string[]
  buildings?: {
    type: string
    count: number
  }[]
  insurance?: {
    provider: string
    policyNumber: string
    expiryDate: string
  }
  notes?: string
  status?: string
}

// Mock data for demo purposes
let mockFarms: Farm[] = [
  {
    id: 1,
    name: 'Ferme Agricole Bamenda',
    location: 'Bamenda, North West Region',
    address: 'Mile 4 Nkwen, Bamenda, Cameroon',
    coordinates: {
      latitude: 5.9631,
      longitude: 10.1591
    },
    size: 180,
    sizeUnit: 'hectares',
    fields: 8,
    crops: ['Maize', 'Cassava', 'Plantain', 'Irish Potato'],
    status: 'Active',
    farmType: 'Mixed',
    soilType: 'Volcanic',
    climate: 'Highland Tropical',
    waterSource: ['River', 'Borehole'],
    irrigation: true,
    certifications: ['Organic Certified', 'Fair Trade'],
    owner: {
      name: 'Ngozi Mbah',
      email: '<EMAIL>',
      phone: '+237 677 123 456'
    },
    manager: {
      name: 'Paul Tabi',
      email: '<EMAIL>',
      phone: '+237 677 123 457'
    },
    established: '2015-06-20',
    totalRevenue: 45000000, // CFA Francs
    totalExpenses: 32000000, // CFA Francs
    employees: 25,
    equipment: ['Tractor', 'Plough', 'Irrigation System', 'Storage Silos'],
    buildings: [
      { type: 'Storage Warehouse', count: 2 },
      { type: 'Processing Center', count: 1 },
      { type: 'Equipment Shed', count: 1 }
    ],
    insurance: {
      provider: 'CIMA Insurance Cameroon',
      policyNumber: 'CIC-2024-BAM-001',
      expiryDate: '2024-12-31'
    },
    notes: 'Exploitation agricole productive dans les hautes terres de Bamenda, spécialisée dans les cultures vivrières.',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 2,
    name: 'Plantation Kribi',
    location: 'Kribi, South Region',
    address: 'Route de Campo, Kribi, Cameroon',
    coordinates: {
      latitude: 2.9373,
      longitude: 9.9073
    },
    size: 250,
    sizeUnit: 'hectares',
    fields: 12,
    crops: ['Cocoa', 'Rubber', 'Palm Oil', 'Tropical Fruits'],
    status: 'Active',
    farmType: 'Mixed',
    soilType: 'Lateritic',
    climate: 'Tropical Humid',
    waterSource: ['River', 'Rainfall'],
    irrigation: false,
    certifications: ['Rainforest Alliance', 'Fair Trade'],
    owner: {
      name: 'Paul Biya Essomba',
      email: '<EMAIL>',
      phone: '+237 699 234 567'
    },
    manager: {
      name: 'Marie Atangana',
      email: '<EMAIL>',
      phone: '+237 699 234 568'
    },
    established: '2008-04-12',
    totalRevenue: 85000000, // CFA Francs
    totalExpenses: 58000000, // CFA Francs
    employees: 45,
    equipment: ['Tractors', 'Processing Equipment', 'Transport Trucks', 'Drying Facilities'],
    buildings: [
      { type: 'Processing Plant', count: 1 },
      { type: 'Storage Warehouse', count: 3 },
      { type: 'Worker Housing', count: 8 }
    ],
    insurance: {
      provider: 'NSIA Assurances Cameroun',
      policyNumber: 'NSIA-2024-KRI-002',
      expiryDate: '2024-11-30'
    },
    notes: 'Grande plantation côtière spécialisée dans les cultures de rente tropicales.',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 3,
    name: 'Mountain View Ranch',
    location: 'Colorado, USA',
    address: '9012 Mountain Highway, Fort Collins, CO 80521',
    coordinates: {
      latitude: 40.5853,
      longitude: -105.0844
    },
    size: 300,
    sizeUnit: 'acres',
    fields: 6,
    crops: ['Barley', 'Alfalfa'],
    status: 'Seasonal',
    farmType: 'Mixed',
    soilType: 'Silt Loam',
    climate: 'Continental',
    waterSource: ['Mountain Stream', 'Snow Melt'],
    irrigation: false,
    certifications: ['Sustainable Agriculture Certified'],
    owner: {
      name: 'Robert Thompson',
      email: '<EMAIL>',
      phone: '+****************'
    },
    manager: {
      name: 'Lisa Chen',
      email: '<EMAIL>',
      phone: '+****************'
    },
    established: '1978-05-10',
    totalRevenue: 320000,
    totalExpenses: 245000,
    employees: 8,
    equipment: ['New Holland Tractor', 'Hay Baler', 'Mower', 'Livestock Trailer'],
    buildings: [
      { type: 'Livestock Barn', count: 2 },
      { type: 'Hay Storage', count: 4 },
      { type: 'Equipment Shed', count: 2 }
    ],
    insurance: {
      provider: 'Ranch & Farm Insurance',
      policyNumber: 'RFI-2024-003',
      expiryDate: '2025-01-15'
    },
    notes: 'High-altitude ranch focusing on livestock and feed production.',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
]

export const farmService = {
  // Get all farms
  async getFarms(): Promise<Farm[]> {
    await new Promise(resolve => setTimeout(resolve, 500))
    return [...mockFarms]
  },

  // Get farm by ID
  async getFarm(id: number): Promise<Farm> {
    await new Promise(resolve => setTimeout(resolve, 300))
    const farm = mockFarms.find(f => f.id === id)
    if (!farm) throw new Error('Farm not found')
    return farm
  },

  // Create new farm
  async createFarm(data: CreateFarmData): Promise<Farm> {
    await new Promise(resolve => setTimeout(resolve, 800))
    const newFarm: Farm = {
      id: Math.max(...mockFarms.map(f => f.id)) + 1,
      ...data,
      fields: 0,
      crops: [],
      status: 'Active',
      totalRevenue: 0,
      totalExpenses: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    mockFarms.push(newFarm)
    return newFarm
  },

  // Update farm
  async updateFarm(id: number, data: UpdateFarmData): Promise<Farm> {
    await new Promise(resolve => setTimeout(resolve, 800))
    const index = mockFarms.findIndex(f => f.id === id)
    if (index === -1) throw new Error('Farm not found')

    mockFarms[index] = {
      ...mockFarms[index],
      ...data,
      updatedAt: new Date().toISOString()
    }
    return mockFarms[index]
  },

  // Delete farm
  async deleteFarm(id: number): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 500))
    const index = mockFarms.findIndex(f => f.id === id)
    if (index === -1) throw new Error('Farm not found')
    mockFarms.splice(index, 1)
  },
}
