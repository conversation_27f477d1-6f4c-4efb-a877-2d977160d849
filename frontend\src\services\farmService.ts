import api from './api'

export interface Farm {
  id: string
  name: string
  location: string
  address: string
  coordinates: {
    latitude: number
    longitude: number
  }
  size: number
  sizeUnit: 'acres' | 'hectares'
  fields: number
  crops: string[]
  status: 'Active' | 'Inactive' | 'Seasonal'
  farmType: 'Crop' | 'Livestock' | 'Mixed' | 'Organic' | 'Dairy'
  soilType: string
  climate: string
  waterSource: string[]
  irrigation: boolean
  certifications: string[]
  owner: {
    name: string
    email: string
    phone: string
  }
  manager: {
    name: string
    email: string
    phone: string
  }
  established: string
  totalRevenue: number
  totalExpenses: number
  employees: number
  equipment: string[]
  buildings: {
    type: string
    count: number
  }[]
  insurance: {
    provider: string
    policyNumber: string
    expiryDate: string
  }
  notes: string
  createdAt: string
  updatedAt: string
}

export interface CreateFarmData {
  name: string
  location: string
  address: string
  coordinates: {
    latitude: number
    longitude: number
  }
  size: number
  sizeUnit: 'acres' | 'hectares'
  farmType: 'Crop' | 'Livestock' | 'Mixed' | 'Organic' | 'Dairy'
  soilType: string
  climate: string
  waterSource: string[]
  irrigation: boolean
  certifications: string[]
  owner: {
    name: string
    email: string
    phone: string
  }
  manager: {
    name: string
    email: string
    phone: string
  }
  established: string
  employees: number
  equipment: string[]
  buildings: {
    type: string
    count: number
  }[]
  insurance: {
    provider: string
    policyNumber: string
    expiryDate: string
  }
  notes: string
}

export interface UpdateFarmData {
  name?: string
  location?: string
  address?: string
  coordinates?: {
    latitude: number
    longitude: number
  }
  size?: number
  sizeUnit?: 'acres' | 'hectares'
  farmType?: 'Crop' | 'Livestock' | 'Mixed' | 'Organic' | 'Dairy'
  soilType?: string
  climate?: string
  waterSource?: string[]
  irrigation?: boolean
  certifications?: string[]
  owner?: {
    name: string
    email: string
    phone: string
  }
  manager?: {
    name: string
    email: string
    phone: string
  }
  established?: string
  employees?: number
  equipment?: string[]
  buildings?: {
    type: string
    count: number
  }[]
  insurance?: {
    provider: string
    policyNumber: string
    expiryDate: string
  }
  notes?: string
  status?: string
}

// Mock data for demo purposes
let mockFarms: Farm[] = [
  {
    id: '1',
    name: 'Ferme Agricole Bamenda',
    location: 'Bamenda, North West Region',
    address: 'Mile 4 Nkwen, Bamenda, Cameroon',
    coordinates: {
      latitude: 5.9631,
      longitude: 10.1591
    },
    size: 180,
    sizeUnit: 'hectares',
    fields: 8,
    crops: ['Maize', 'Cassava', 'Plantain', 'Irish Potato'],
    status: 'Active',
    farmType: 'Mixed',
    soilType: 'Volcanic',
    climate: 'Highland Tropical',
    waterSource: ['River', 'Borehole'],
    irrigation: true,
    certifications: ['Organic Certified', 'Fair Trade'],
    owner: {
      name: 'Ngozi Mbah',
      email: '<EMAIL>',
      phone: '+237 677 123 456'
    },
    manager: {
      name: 'Paul Tabi',
      email: '<EMAIL>',
      phone: '+237 677 123 457'
    },
    established: '2015-06-20',
    totalRevenue: 45000000, // CFA Francs
    totalExpenses: 32000000, // CFA Francs
    employees: 25,
    equipment: ['Tractor', 'Plough', 'Irrigation System', 'Storage Silos'],
    buildings: [
      { type: 'Storage Warehouse', count: 2 },
      { type: 'Processing Center', count: 1 },
      { type: 'Equipment Shed', count: 1 }
    ],
    insurance: {
      provider: 'CIMA Insurance Cameroon',
      policyNumber: 'CIC-2024-BAM-001',
      expiryDate: '2024-12-31'
    },
    notes: 'Exploitation agricole productive dans les hautes terres de Bamenda, spécialisée dans les cultures vivrières.',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '2',
    name: 'Plantation Kribi',
    location: 'Kribi, South Region',
    address: 'Route de Campo, Kribi, Cameroon',
    coordinates: {
      latitude: 2.9373,
      longitude: 9.9073
    },
    size: 250,
    sizeUnit: 'hectares',
    fields: 12,
    crops: ['Cocoa', 'Rubber', 'Palm Oil', 'Tropical Fruits'],
    status: 'Active',
    farmType: 'Mixed',
    soilType: 'Lateritic',
    climate: 'Tropical Humid',
    waterSource: ['River', 'Rainfall'],
    irrigation: false,
    certifications: ['Rainforest Alliance', 'Fair Trade'],
    owner: {
      name: 'Paul Biya Essomba',
      email: '<EMAIL>',
      phone: '+237 699 234 567'
    },
    manager: {
      name: 'Marie Atangana',
      email: '<EMAIL>',
      phone: '+237 699 234 568'
    },
    established: '2008-04-12',
    totalRevenue: 85000000, // CFA Francs
    totalExpenses: 58000000, // CFA Francs
    employees: 45,
    equipment: ['Tractors', 'Processing Equipment', 'Transport Trucks', 'Drying Facilities'],
    buildings: [
      { type: 'Processing Plant', count: 1 },
      { type: 'Storage Warehouse', count: 3 },
      { type: 'Worker Housing', count: 8 }
    ],
    insurance: {
      provider: 'NSIA Assurances Cameroun',
      policyNumber: 'NSIA-2024-KRI-002',
      expiryDate: '2024-11-30'
    },
    notes: 'Grande plantation côtière spécialisée dans les cultures de rente tropicales.',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '3',
    name: 'Ferme Maroua',
    location: 'Maroua, Far North Region',
    address: 'Route de Mokolo, Maroua, Cameroon',
    coordinates: {
      latitude: 10.5906,
      longitude: 14.3158
    },
    size: 120,
    sizeUnit: 'hectares',
    fields: 6,
    crops: ['Millet', 'Sorghum', 'Cotton', 'Groundnuts'],
    status: 'Active',
    farmType: 'Crop',
    soilType: 'Sandy',
    climate: 'Sahelian',
    waterSource: ['Borehole', 'Seasonal River'],
    irrigation: true,
    certifications: ['Organic Certified'],
    owner: {
      name: 'Aminatou Alhadji',
      email: '<EMAIL>',
      phone: '+237 655 345 678'
    },
    manager: {
      name: 'Ibrahim Moussa',
      email: '<EMAIL>',
      phone: '+237 655 345 679'
    },
    established: '2012-09-15',
    totalRevenue: 28000000, // CFA Francs
    totalExpenses: 20000000, // CFA Francs
    employees: 18,
    equipment: ['Tractor', 'Plough', 'Irrigation Equipment', 'Harvester'],
    buildings: [
      { type: 'Storage Warehouse', count: 2 },
      { type: 'Grain Silo', count: 3 },
      { type: 'Equipment Shed', count: 1 }
    ],
    insurance: {
      provider: 'CIMA Assurances Cameroun',
      policyNumber: 'CIC-2024-MAR-003',
      expiryDate: '2025-01-15'
    },
    notes: 'Exploitation agricole sahélienne concentrée sur les céréales et les cultures de rente.',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '4',
    name: 'Agro-Ferme Bafoussam',
    location: 'Bafoussam, West Region',
    address: 'Route de Dschang, Bafoussam, Cameroon',
    coordinates: {
      latitude: 5.4737,
      longitude: 10.4178
    },
    size: 95,
    sizeUnit: 'hectares',
    fields: 5,
    crops: ['Coffee', 'Maize', 'Beans'],
    status: 'Active',
    farmType: 'Mixed',
    soilType: 'Volcanic',
    climate: 'Tropical Highland',
    waterSource: ['Spring', 'Rainfall'],
    irrigation: false,
    certifications: ['Fair Trade Certified'],
    owner: {
      name: 'François Kamga',
      email: '<EMAIL>',
      phone: '+237 677 456 789'
    },
    manager: {
      name: 'Célestine Tchinda',
      email: '<EMAIL>',
      phone: '+237 677 456 790'
    },
    established: '2018-01-10',
    totalRevenue: 15000000, // CFA Francs
    totalExpenses: 11000000, // CFA Francs
    employees: 8,
    equipment: ['Coffee Processing Equipment', 'Drying Beds', 'Storage Facilities'],
    buildings: [
      { type: 'Coffee Processing Unit', count: 1 },
      { type: 'Storage Warehouse', count: 1 },
      { type: 'Drying Facility', count: 2 }
    ],
    insurance: {
      provider: 'CIMA Assurances Cameroun',
      policyNumber: 'CIC-2024-BAF-004',
      expiryDate: '2025-01-10'
    },
    notes: 'Ferme spécialisée dans le café arabica des hautes terres de l\'Ouest.',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '5',
    name: 'Plantation Bertoua',
    location: 'Bertoua, East Region',
    address: 'Route de Batouri, Bertoua, Cameroon',
    coordinates: {
      latitude: 4.5774,
      longitude: 13.6848
    },
    size: 300,
    sizeUnit: 'hectares',
    fields: 3,
    crops: ['Rubber', 'Palm Oil', 'Timber'],
    status: 'Active',
    farmType: 'Mixed',
    soilType: 'Forest',
    climate: 'Equatorial',
    waterSource: ['River', 'Rainfall'],
    irrigation: false,
    certifications: ['RSPO Certified', 'FSC Certified'],
    owner: {
      name: 'André Mbida Essomba',
      email: '<EMAIL>',
      phone: '+237 655 567 890'
    },
    manager: {
      name: 'Joséphine Ngo Biyaga',
      email: '<EMAIL>',
      phone: '+237 655 567 891'
    },
    established: '2008-06-25',
    totalRevenue: 41000000, // CFA Francs
    totalExpenses: 28000000, // CFA Francs
    employees: 35,
    equipment: ['Rubber Tapping Equipment', 'Palm Oil Mill', 'Timber Processing'],
    buildings: [
      { type: 'Palm Oil Mill', count: 1 },
      { type: 'Rubber Processing Plant', count: 1 },
      { type: 'Timber Mill', count: 1 },
      { type: 'Worker Housing', count: 8 }
    ],
    insurance: {
      provider: 'CIMA Assurances Cameroun',
      policyNumber: 'CIC-2024-BER-005',
      expiryDate: '2025-06-25'
    },
    notes: 'Grande plantation forestière avec transformation sur site.',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
]

export const farmService = {
  // Get all farms
  async getFarms(): Promise<Farm[]> {
    try {
      const response = await fetch('http://localhost:3001/api/farms')
      const data = await response.json()

      if (data.success) {
        // Transform MongoDB data to match our interface
        return data.data.map((farm: any) => ({
          id: farm._id,
          name: farm.name,
          location: farm.location,
          address: farm.address,
          coordinates: farm.coordinates,
          size: farm.size,
          sizeUnit: farm.sizeUnit,
          fields: farm.fields,
          crops: farm.crops,
          status: farm.status,
          farmType: farm.farmType,
          soilType: farm.soilType,
          climate: farm.climate,
          waterSource: farm.waterSource,
          irrigation: farm.irrigation,
          certifications: farm.certifications,
          owner: farm.owner,
          manager: farm.manager,
          established: farm.established,
          totalRevenue: farm.totalRevenue,
          totalExpenses: farm.totalExpenses,
          employees: farm.employees,
          equipment: farm.equipment,
          buildings: farm.buildings,
          insurance: farm.insurance,
          notes: farm.notes,
          createdAt: farm.createdAt,
          updatedAt: farm.updatedAt
        }))
      } else {
        throw new Error('Failed to fetch farms')
      }
    } catch (error) {
      console.error('Error fetching farms:', error)
      // Fallback to mock data if API fails
      return [...mockFarms]
    }
  },

  // Get farm by ID
  async getFarm(id: string): Promise<Farm> {
    try {
      const farms = await this.getFarms()
      const farm = farms.find(f => f.id === id)
      if (!farm) throw new Error('Farm not found')
      return farm
    } catch (error) {
      console.error('Error fetching farm:', error)
      throw new Error('Farm not found')
    }
  },

  // Create new farm
  async createFarm(data: CreateFarmData): Promise<Farm> {
    await new Promise(resolve => setTimeout(resolve, 800))
    const newFarm: Farm = {
      id: (mockFarms.length + 1).toString(),
      ...data,
      fields: 0,
      crops: [],
      status: 'Active',
      totalRevenue: 0,
      totalExpenses: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    mockFarms.push(newFarm)
    return newFarm
  },

  // Update farm
  async updateFarm(id: string, data: UpdateFarmData): Promise<Farm> {
    await new Promise(resolve => setTimeout(resolve, 800))
    const index = mockFarms.findIndex(f => f.id === id)
    if (index === -1) throw new Error('Farm not found')

    mockFarms[index] = {
      ...mockFarms[index],
      ...data,
      updatedAt: new Date().toISOString()
    }
    return mockFarms[index]
  },

  // Delete farm
  async deleteFarm(id: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 500))
    const index = mockFarms.findIndex(f => f.id === id)
    if (index === -1) throw new Error('Farm not found')
    mockFarms.splice(index, 1)
  },
}
