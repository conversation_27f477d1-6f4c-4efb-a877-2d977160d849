import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { X, MapPin, Users, Building, Shield } from 'lucide-react'
import { Farm, CreateFarmData, UpdateFarmData } from '../services/farmService'
import FarmDetailMap from './FarmDetailMap'

interface FarmModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: CreateFarmData | UpdateFarmData) => Promise<void>
  farm?: Farm | null
  loading?: boolean
}

export default function FarmModal({ isOpen, onClose, onSubmit, farm, loading }: FarmModalProps) {
  const { register, handleSubmit, formState: { errors }, reset, watch } = useForm<CreateFarmData>()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [activeTab, setActiveTab] = useState('basic')

  const tabs = [
    { id: 'basic', label: 'Basic Info', icon: MapPin },
    { id: 'contacts', label: 'Contacts', icon: Users },
    { id: 'infrastructure', label: 'Infrastructure', icon: Building },
    { id: 'insurance', label: 'Insurance', icon: Shield },
  ]

  useEffect(() => {
    if (farm) {
      reset({
        name: farm.name,
        location: farm.location,
        address: farm.address,
        coordinates: farm.coordinates,
        size: farm.size,
        sizeUnit: farm.sizeUnit,
        farmType: farm.farmType,
        soilType: farm.soilType,
        climate: farm.climate,
        waterSource: farm.waterSource,
        irrigation: farm.irrigation,
        certifications: farm.certifications,
        owner: farm.owner,
        manager: farm.manager,
        established: farm.established,
        employees: farm.employees,
        equipment: farm.equipment,
        buildings: farm.buildings,
        insurance: farm.insurance,
        notes: farm.notes,
      })
    } else {
      reset({
        name: '',
        location: '',
        address: '',
        coordinates: { latitude: 0, longitude: 0 },
        size: 0,
        sizeUnit: 'acres',
        farmType: 'Crop',
        soilType: '',
        climate: '',
        waterSource: [],
        irrigation: false,
        certifications: [],
        owner: { name: '', email: '', phone: '' },
        manager: { name: '', email: '', phone: '' },
        established: '',
        employees: 0,
        equipment: [],
        buildings: [],
        insurance: { provider: '', policyNumber: '', expiryDate: '' },
        notes: '',
      })
    }
  }, [farm, reset])

  const handleFormSubmit = async (data: CreateFarmData) => {
    setIsSubmitting(true)
    try {
      await onSubmit(data)
      onClose()
      reset()
    } catch (error) {
      console.error('Error submitting farm:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={onClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full">
          <div className="flex items-center justify-between p-6 border-b">
            <h3 className="text-lg font-medium text-gray-900">
              {farm ? 'Edit Farm' : 'Add New Farm'}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(handleFormSubmit)} className="p-6 space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Farm Name *
              </label>
              <input
                {...register('name', { required: 'Farm name is required' })}
                type="text"
                className="input mt-1"
                placeholder="Enter farm name"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="location" className="block text-sm font-medium text-gray-700">
                Location *
              </label>
              <input
                {...register('location', { required: 'Location is required' })}
                type="text"
                className="input mt-1"
                placeholder="Enter location"
              />
              {errors.location && (
                <p className="mt-1 text-sm text-red-600">{errors.location.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="size" className="block text-sm font-medium text-gray-700">
                Size *
              </label>
              <input
                {...register('size', { required: 'Size is required' })}
                type="text"
                className="input mt-1"
                placeholder="e.g., 250 acres"
              />
              {errors.size && (
                <p className="mt-1 text-sm text-red-600">{errors.size.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                📍 Farm Coordinates (Optional)
              </label>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="latitude" className="block text-xs font-medium text-gray-600">
                    Latitude
                  </label>
                  <input
                    {...register('coordinates.latitude', {
                      valueAsNumber: true,
                      min: { value: -90, message: 'Invalid latitude' },
                      max: { value: 90, message: 'Invalid latitude' }
                    })}
                    type="number"
                    step="any"
                    className="input mt-1"
                    placeholder="40.7128"
                  />
                </div>

                <div>
                  <label htmlFor="longitude" className="block text-xs font-medium text-gray-600">
                    Longitude
                  </label>
                  <input
                    {...register('coordinates.longitude', {
                      valueAsNumber: true,
                      min: { value: -180, message: 'Invalid longitude' },
                      max: { value: 180, message: 'Invalid longitude' }
                    })}
                    type="number"
                    step="any"
                    className="input mt-1"
                    placeholder="-74.0060"
                  />
                </div>
              </div>
              <p className="mt-1 text-xs text-gray-500">
                💡 Add coordinates to show your farm on the map
              </p>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="btn-outline"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn-primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : farm ? 'Update Farm' : 'Add Farm'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
