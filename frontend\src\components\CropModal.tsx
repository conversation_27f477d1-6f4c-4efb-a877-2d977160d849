import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { X } from 'lucide-react'
import { Crop, CreateCropData, UpdateCropData } from '../services/cropService'

interface CropModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: CreateCropData | UpdateCropData) => Promise<void>
  crop?: Crop | null
  loading?: boolean
}

export default function CropModal({ isOpen, onClose, onSubmit, crop, loading }: CropModalProps) {
  const { register, handleSubmit, formState: { errors }, reset, watch } = useForm<CreateCropData>()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const growthStage = watch('growthStage')

  useEffect(() => {
    if (crop) {
      reset({
        name: crop.name,
        variety: crop.variety,
        fieldId: crop.fieldId,
        plantingDate: crop.plantingDate,
        expectedHarvestDate: crop.expectedHarvestDate,
        area: crop.area,
        unit: crop.unit,
        growthStage: crop.growthStage,
        healthStatus: crop.healthStatus,
        expectedYield: crop.expectedYield,
        yieldUnit: crop.yieldUnit,
        notes: crop.notes || '',
        irrigationSchedule: crop.irrigationSchedule || '',
        fertilizationPlan: crop.fertilizationPlan || '',
        pestManagement: crop.pestManagement || '',
        cost: crop.cost,
      })
    } else {
      reset({
        name: '',
        variety: '',
        fieldId: 1,
        plantingDate: '',
        expectedHarvestDate: '',
        area: 0,
        unit: 'acres',
        growthStage: 'Planted',
        healthStatus: 'Good',
        expectedYield: 0,
        yieldUnit: 'tons/acre',
        notes: '',
        irrigationSchedule: '',
        fertilizationPlan: '',
        pestManagement: '',
        cost: 0,
      })
    }
  }, [crop, reset])

  const handleFormSubmit = async (data: CreateCropData) => {
    setIsSubmitting(true)
    try {
      await onSubmit(data)
      onClose()
      reset()
    } catch (error) {
      console.error('Error submitting crop:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={onClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <h3 className="text-lg font-medium text-gray-900">
              {crop ? 'Edit Crop' : 'Add New Crop'}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(handleFormSubmit)} className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Crop Name *
                </label>
                <input
                  {...register('name', { required: 'Crop name is required' })}
                  type="text"
                  className="input mt-1"
                  placeholder="e.g., Corn, Wheat, Soybeans"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="variety" className="block text-sm font-medium text-gray-700">
                  Variety *
                </label>
                <input
                  {...register('variety', { required: 'Variety is required' })}
                  type="text"
                  className="input mt-1"
                  placeholder="e.g., Sweet Corn Hybrid"
                />
                {errors.variety && (
                  <p className="mt-1 text-sm text-red-600">{errors.variety.message}</p>
                )}
              </div>
            </div>

            {/* Field and Area */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="fieldId" className="block text-sm font-medium text-gray-700">
                  Field *
                </label>
                <select
                  {...register('fieldId', { required: 'Field is required' })}
                  className="input mt-1"
                >
                  <option value={1}>North Field A</option>
                  <option value={2}>South Field B</option>
                  <option value={3}>East Field C</option>
                </select>
                {errors.fieldId && (
                  <p className="mt-1 text-sm text-red-600">{errors.fieldId.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="area" className="block text-sm font-medium text-gray-700">
                  Area *
                </label>
                <input
                  {...register('area', { 
                    required: 'Area is required',
                    min: { value: 0.1, message: 'Area must be greater than 0' }
                  })}
                  type="number"
                  step="0.1"
                  className="input mt-1"
                  placeholder="25"
                />
                {errors.area && (
                  <p className="mt-1 text-sm text-red-600">{errors.area.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="unit" className="block text-sm font-medium text-gray-700">
                  Unit
                </label>
                <select
                  {...register('unit')}
                  className="input mt-1"
                >
                  <option value="acres">Acres</option>
                  <option value="hectares">Hectares</option>
                  <option value="sq ft">Square Feet</option>
                </select>
              </div>
            </div>

            {/* Dates */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="plantingDate" className="block text-sm font-medium text-gray-700">
                  Planting Date *
                </label>
                <input
                  {...register('plantingDate', { required: 'Planting date is required' })}
                  type="date"
                  className="input mt-1"
                />
                {errors.plantingDate && (
                  <p className="mt-1 text-sm text-red-600">{errors.plantingDate.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="expectedHarvestDate" className="block text-sm font-medium text-gray-700">
                  Expected Harvest Date *
                </label>
                <input
                  {...register('expectedHarvestDate', { required: 'Expected harvest date is required' })}
                  type="date"
                  className="input mt-1"
                />
                {errors.expectedHarvestDate && (
                  <p className="mt-1 text-sm text-red-600">{errors.expectedHarvestDate.message}</p>
                )}
              </div>
            </div>

            {/* Status and Health */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="growthStage" className="block text-sm font-medium text-gray-700">
                  Growth Stage
                </label>
                <select
                  {...register('growthStage')}
                  className="input mt-1"
                >
                  <option value="Planted">Planted</option>
                  <option value="Germination">Germination</option>
                  <option value="Vegetative">Vegetative</option>
                  <option value="Flowering">Flowering</option>
                  <option value="Maturity">Maturity</option>
                  <option value="Harvested">Harvested</option>
                </select>
              </div>

              <div>
                <label htmlFor="healthStatus" className="block text-sm font-medium text-gray-700">
                  Health Status
                </label>
                <select
                  {...register('healthStatus')}
                  className="input mt-1"
                >
                  <option value="Excellent">Excellent</option>
                  <option value="Good">Good</option>
                  <option value="Fair">Fair</option>
                  <option value="Poor">Poor</option>
                  <option value="Critical">Critical</option>
                </select>
              </div>
            </div>

            {/* Yield and Cost */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="expectedYield" className="block text-sm font-medium text-gray-700">
                  Expected Yield *
                </label>
                <input
                  {...register('expectedYield', { 
                    required: 'Expected yield is required',
                    min: { value: 0, message: 'Yield must be greater than 0' }
                  })}
                  type="number"
                  step="0.1"
                  className="input mt-1"
                  placeholder="200"
                />
                {errors.expectedYield && (
                  <p className="mt-1 text-sm text-red-600">{errors.expectedYield.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="yieldUnit" className="block text-sm font-medium text-gray-700">
                  Yield Unit
                </label>
                <select
                  {...register('yieldUnit')}
                  className="input mt-1"
                >
                  <option value="tons/acre">Tons/Acre</option>
                  <option value="bushels/acre">Bushels/Acre</option>
                  <option value="lbs/acre">Lbs/Acre</option>
                  <option value="kg/hectare">Kg/Hectare</option>
                </select>
              </div>

              <div>
                <label htmlFor="cost" className="block text-sm font-medium text-gray-700">
                  Total Cost (CFA)
                </label>
                <input
                  {...register('cost', { 
                    min: { value: 0, message: 'Cost must be greater than or equal to 0' }
                  })}
                  type="number"
                  step="0.01"
                  className="input mt-1"
                  placeholder="2500"
                />
                {errors.cost && (
                  <p className="mt-1 text-sm text-red-600">{errors.cost.message}</p>
                )}
              </div>
            </div>

            {/* Management Plans */}
            <div className="space-y-4">
              <div>
                <label htmlFor="irrigationSchedule" className="block text-sm font-medium text-gray-700">
                  Irrigation Schedule
                </label>
                <input
                  {...register('irrigationSchedule')}
                  type="text"
                  className="input mt-1"
                  placeholder="Every 3 days, 2 inches"
                />
              </div>

              <div>
                <label htmlFor="fertilizationPlan" className="block text-sm font-medium text-gray-700">
                  Fertilization Plan
                </label>
                <input
                  {...register('fertilizationPlan')}
                  type="text"
                  className="input mt-1"
                  placeholder="NPK 20-10-10 applied monthly"
                />
              </div>

              <div>
                <label htmlFor="pestManagement" className="block text-sm font-medium text-gray-700">
                  Pest Management
                </label>
                <input
                  {...register('pestManagement')}
                  type="text"
                  className="input mt-1"
                  placeholder="Integrated pest management approach"
                />
              </div>

              <div>
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                  Notes
                </label>
                <textarea
                  {...register('notes')}
                  rows={3}
                  className="input mt-1"
                  placeholder="Additional notes about this crop..."
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t">
              <button
                type="button"
                onClick={onClose}
                className="btn-outline"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn-primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : crop ? 'Update Crop' : 'Add Crop'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
