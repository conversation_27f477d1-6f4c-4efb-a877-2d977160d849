import { useState, useEffect } from 'react'
import { 
  FileText, 
  Download, 
  Calendar, 
  TrendingUp, 
  BarChart3, 
  <PERSON><PERSON><PERSON>,
  Filter,
  <PERSON>freshCw,
  MapPin,
  DollarSign,
  Users,
  Sprout
} from 'lucide-react'

interface Farm {
  id: string
  name: string
  location: string
  totalRevenue: number
  totalExpenses: number
  employees: number
  fields: number
  crops: string[]
}

interface ReportData {
  farms: Farm[]
  totalRevenue: number
  totalExpenses: number
  totalProfit: number
  totalEmployees: number
  totalFields: number
  totalCrops: number
}

export default function Reports() {
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState('monthly')
  const [selectedFarm, setSelectedFarm] = useState('all')
  const [reportType, setReportType] = useState('financial')

  useEffect(() => {
    fetchReportData()
  }, [selectedPeriod, selectedFarm])

  const fetchReportData = async () => {
    setLoading(true)
    try {
      const response = await fetch('http://localhost:3001/api/farms')
      const data = await response.json()
      
      if (data.success) {
        const farms = data.data
        const totalRevenue = farms.reduce((sum: number, farm: any) => sum + (farm.totalRevenue || 0), 0)
        const totalExpenses = farms.reduce((sum: number, farm: any) => sum + (farm.totalExpenses || 0), 0)
        const totalEmployees = farms.reduce((sum: number, farm: any) => sum + (farm.employees || 0), 0)
        const totalFields = farms.reduce((sum: number, farm: any) => sum + (farm.fields || 0), 0)
        const allCrops = farms.flatMap((farm: any) => farm.crops || [])
        const uniqueCrops = [...new Set(allCrops)].length

        setReportData({
          farms: farms.map((farm: any) => ({
            id: farm._id,
            name: farm.name,
            location: farm.location,
            totalRevenue: farm.totalRevenue || 0,
            totalExpenses: farm.totalExpenses || 0,
            employees: farm.employees || 0,
            fields: farm.fields || 0,
            crops: farm.crops || []
          })),
          totalRevenue,
          totalExpenses,
          totalProfit: totalRevenue - totalExpenses,
          totalEmployees,
          totalFields,
          totalCrops: uniqueCrops
        })
      }
    } catch (error) {
      console.error('Error fetching report data:', error)
    } finally {
      setLoading(false)
    }
  }

  const generateReport = (type: string) => {
    if (!reportData) return

    const reportContent = {
      financial: generateFinancialReport(),
      operational: generateOperationalReport(),
      productivity: generateProductivityReport()
    }

    const content = reportContent[type as keyof typeof reportContent] || reportContent.financial
    
    // Create and download the report
    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `farm-${type}-report-${new Date().toISOString().split('T')[0]}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const generateFinancialReport = () => {
    if (!reportData) return ''

    return `
FARM FINANCIAL REPORT
Generated: ${new Date().toLocaleDateString()}
Period: ${selectedPeriod.toUpperCase()}

=== SUMMARY ===
Total Revenue: ${new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'XAF' }).format(reportData.totalRevenue)}
Total Expenses: ${new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'XAF' }).format(reportData.totalExpenses)}
Net Profit: ${new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'XAF' }).format(reportData.totalProfit)}
Profit Margin: ${((reportData.totalProfit / reportData.totalRevenue) * 100).toFixed(2)}%

=== FARM BREAKDOWN ===
${reportData.farms.map(farm => `
${farm.name} (${farm.location})
  Revenue: ${new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'XAF' }).format(farm.totalRevenue)}
  Expenses: ${new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'XAF' }).format(farm.totalExpenses)}
  Profit: ${new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'XAF' }).format(farm.totalRevenue - farm.totalExpenses)}
  Employees: ${farm.employees}
`).join('')}

=== RECOMMENDATIONS ===
- Focus on farms with highest profit margins
- Review expenses for underperforming farms
- Consider expansion opportunities for profitable farms
`
  }

  const generateOperationalReport = () => {
    if (!reportData) return ''

    return `
FARM OPERATIONAL REPORT
Generated: ${new Date().toLocaleDateString()}

=== OPERATIONAL SUMMARY ===
Total Farms: ${reportData.farms.length}
Total Fields: ${reportData.totalFields}
Total Employees: ${reportData.totalEmployees}
Crop Varieties: ${reportData.totalCrops}

=== FARM DETAILS ===
${reportData.farms.map(farm => `
${farm.name}
  Location: ${farm.location}
  Fields: ${farm.fields}
  Employees: ${farm.employees}
  Crops: ${farm.crops.join(', ')}
`).join('')}
`
  }

  const generateProductivityReport = () => {
    if (!reportData) return ''

    return `
FARM PRODUCTIVITY REPORT
Generated: ${new Date().toLocaleDateString()}

=== PRODUCTIVITY METRICS ===
Average Revenue per Farm: ${new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'XAF' }).format(reportData.totalRevenue / reportData.farms.length)}
Average Revenue per Employee: ${new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'XAF' }).format(reportData.totalRevenue / reportData.totalEmployees)}
Average Fields per Farm: ${(reportData.totalFields / reportData.farms.length).toFixed(1)}

=== FARM PERFORMANCE ===
${reportData.farms.map(farm => {
  const revenuePerEmployee = farm.employees > 0 ? farm.totalRevenue / farm.employees : 0
  const revenuePerField = farm.fields > 0 ? farm.totalRevenue / farm.fields : 0
  
  return `
${farm.name}
  Revenue per Employee: ${new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'XAF' }).format(revenuePerEmployee)}
  Revenue per Field: ${new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'XAF' }).format(revenuePerField)}
  Efficiency Score: ${((revenuePerEmployee + revenuePerField) / 2000000 * 100).toFixed(1)}%
`
}).join('')}
`
  }

  const scheduleReport = () => {
    alert(`Report scheduled for ${selectedPeriod} delivery. You will receive notifications when reports are ready.`)
  }

  if (loading) {
    return (
      <div className="farm-bg-reports min-h-screen -m-6 p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading report data...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="farm-bg-reports min-h-screen -m-6 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <FileText className="mr-3 h-8 w-8 text-green-600" />
                Farm Reports & Analytics
              </h1>
              <p className="mt-2 text-gray-600">
                Generate comprehensive reports and analyze farm performance across all operations
              </p>
            </div>
            <button
              onClick={fetchReportData}
              className="btn-secondary flex items-center"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh Data
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <Filter className="h-5 w-5 text-gray-500" />
            <h3 className="text-lg font-medium text-gray-900">Report Filters</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Report Period
              </label>
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="input-field"
              >
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
                <option value="quarterly">Quarterly</option>
                <option value="yearly">Yearly</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Farm Selection
              </label>
              <select
                value={selectedFarm}
                onChange={(e) => setSelectedFarm(e.target.value)}
                className="input-field"
              >
                <option value="all">All Farms</option>
                {reportData?.farms.map(farm => (
                  <option key={farm.id} value={farm.id}>{farm.name}</option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Report Type
              </label>
              <select
                value={reportType}
                onChange={(e) => setReportType(e.target.value)}
                className="input-field"
              >
                <option value="financial">Financial Report</option>
                <option value="operational">Operational Report</option>
                <option value="productivity">Productivity Report</option>
              </select>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        {reportData && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DollarSign className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {new Intl.NumberFormat('fr-FR', {
                      style: 'currency',
                      currency: 'XAF',
                      minimumFractionDigits: 0
                    }).format(reportData.totalRevenue)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TrendingUp className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Net Profit</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {new Intl.NumberFormat('fr-FR', {
                      style: 'currency',
                      currency: 'XAF',
                      minimumFractionDigits: 0
                    }).format(reportData.totalProfit)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Users className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Employees</p>
                  <p className="text-2xl font-bold text-gray-900">{reportData.totalEmployees}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Sprout className="h-8 w-8 text-green-500" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Active Fields</p>
                  <p className="text-2xl font-bold text-gray-900">{reportData.totalFields}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Report Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Generate Reports */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <Download className="mr-2 h-5 w-5 text-green-600" />
              Generate Reports
            </h3>

            <div className="space-y-4">
              <button
                onClick={() => generateReport('financial')}
                className="w-full btn-primary flex items-center justify-center"
              >
                <BarChart3 className="mr-2 h-4 w-4" />
                Financial Report
              </button>

              <button
                onClick={() => generateReport('operational')}
                className="w-full btn-secondary flex items-center justify-center"
              >
                <MapPin className="mr-2 h-4 w-4" />
                Operational Report
              </button>

              <button
                onClick={() => generateReport('productivity')}
                className="w-full btn-secondary flex items-center justify-center"
              >
                <PieChart className="mr-2 h-4 w-4" />
                Productivity Report
              </button>
            </div>
          </div>

          {/* Schedule Reports */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <Calendar className="mr-2 h-5 w-5 text-blue-600" />
              Schedule Reports
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Delivery Frequency
                </label>
                <select className="input-field">
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Recipients
                </label>
                <input
                  type="email"
                  placeholder="<EMAIL>"
                  className="input-field"
                />
              </div>

              <button
                onClick={scheduleReport}
                className="w-full btn-primary flex items-center justify-center"
              >
                <Calendar className="mr-2 h-4 w-4" />
                Schedule Report
              </button>
            </div>
          </div>
        </div>

        {/* Farm Performance Table */}
        {reportData && (
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Farm Performance Overview</h3>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Farm Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Location
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Revenue
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Profit
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Employees
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Fields
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {reportData.farms.map((farm) => {
                    const profit = farm.totalRevenue - farm.totalExpenses
                    const profitMargin = farm.totalRevenue > 0 ? (profit / farm.totalRevenue) * 100 : 0

                    return (
                      <tr key={farm.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{farm.name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500">{farm.location}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {new Intl.NumberFormat('fr-FR', {
                              style: 'currency',
                              currency: 'XAF',
                              minimumFractionDigits: 0
                            }).format(farm.totalRevenue)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className={`text-sm ${profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {new Intl.NumberFormat('fr-FR', {
                              style: 'currency',
                              currency: 'XAF',
                              minimumFractionDigits: 0
                            }).format(profit)}
                            <span className="text-xs ml-1">
                              ({profitMargin.toFixed(1)}%)
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{farm.employees}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{farm.fields}</div>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
