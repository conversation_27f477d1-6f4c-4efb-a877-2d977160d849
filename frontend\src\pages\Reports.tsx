import { useState, useEffect } from 'react'
import { 
  FileText, 
  Download, 
  Calendar, 
  TrendingUp, 
  BarChart3, 
  <PERSON><PERSON><PERSON>,
  Filter,
  <PERSON>freshC<PERSON>,
  MapPin,
  DollarSign,
  Users,
  Sprout
} from 'lucide-react'

interface Farm {
  id: string
  name: string
  location: string
  totalRevenue: number
  totalExpenses: number
  employees: number
  fields: number
  crops: string[]
}

interface ReportData {
  farms: Farm[]
  totalRevenue: number
  totalExpenses: number
  totalProfit: number
  totalEmployees: number
  totalFields: number
  totalCrops: number
}

export default function Reports() {
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [loading, setLoading] = useState(true)
  const [generating, setGenerating] = useState(false)
  const [selectedPeriod, setSelectedPeriod] = useState('monthly')
  const [selectedFarm, setSelectedFarm] = useState('all')
  const [reportType, setReportType] = useState('financial')

  useEffect(() => {
    fetchReportData()
  }, [selectedPeriod, selectedFarm])

  const fetchReportData = async () => {
    setLoading(true)
    try {
      const response = await fetch('http://localhost:3001/api/farms')
      const data = await response.json()
      
      if (data.success) {
        const farms = data.data
        const totalRevenue = farms.reduce((sum: number, farm: any) => sum + (farm.totalRevenue || 0), 0)
        const totalExpenses = farms.reduce((sum: number, farm: any) => sum + (farm.totalExpenses || 0), 0)
        const totalEmployees = farms.reduce((sum: number, farm: any) => sum + (farm.employees || 0), 0)
        const totalFields = farms.reduce((sum: number, farm: any) => sum + (farm.fields || 0), 0)
        const allCrops = farms.flatMap((farm: any) => farm.crops || [])
        const uniqueCrops = [...new Set(allCrops)].length

        setReportData({
          farms: farms.map((farm: any) => ({
            id: farm._id,
            name: farm.name,
            location: farm.location,
            totalRevenue: farm.totalRevenue || 0,
            totalExpenses: farm.totalExpenses || 0,
            employees: farm.employees || 0,
            fields: farm.fields || 0,
            crops: farm.crops || []
          })),
          totalRevenue,
          totalExpenses,
          totalProfit: totalRevenue - totalExpenses,
          totalEmployees,
          totalFields,
          totalCrops: uniqueCrops
        })
      }
    } catch (error) {
      console.error('Error fetching report data:', error)
    } finally {
      setLoading(false)
    }
  }

  const generateReport = async (type: string) => {
    if (!reportData) {
      alert('No data available for report generation. Please wait for data to load.')
      return
    }

    if (generating) {
      alert('Report generation in progress. Please wait...')
      return
    }

    setGenerating(true)

    try {
      // Add small delay to show loading state
      await new Promise(resolve => setTimeout(resolve, 500))
      let content = ''

      switch (type) {
        case 'financial':
          content = generateFinancialReport()
          break
        case 'operational':
          content = generateOperationalReport()
          break
        case 'productivity':
          content = generateProductivityReport()
          break
        default:
          content = generateFinancialReport()
      }

      if (!content) {
        alert('Error generating report content.')
        return
      }

      // Create and download the report
      const timestamp = new Date().toISOString().split('T')[0]
      const filename = `farm-${type}-report-${timestamp}.txt`

      // Try modern download method first
      if (window.navigator && window.navigator.msSaveOrOpenBlob) {
        // For IE/Edge
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
        window.navigator.msSaveOrOpenBlob(blob, filename)
      } else {
        // For modern browsers
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = filename
        a.style.display = 'none'
        a.target = '_blank'
        document.body.appendChild(a)

        // Trigger download
        a.click()

        // Cleanup
        setTimeout(() => {
          document.body.removeChild(a)
          URL.revokeObjectURL(url)
        }, 100)
      }

      // Show success message
      setTimeout(() => {
        alert(`✅ ${type.charAt(0).toUpperCase() + type.slice(1)} report generated successfully!\n\nFile: ${filename}\nSize: ${(content.length / 1024).toFixed(1)} KB`)
      }, 200)
    } catch (error) {
      console.error('Error generating report:', error)
      alert('Error generating report. Please try again.')
    } finally {
      setGenerating(false)
    }
  }

  const generateFinancialReport = () => {
    if (!reportData) return ''

    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: 'XAF',
        minimumFractionDigits: 0
      }).format(amount)
    }

    const profitMargin = reportData.totalRevenue > 0 ?
      ((reportData.totalProfit / reportData.totalRevenue) * 100).toFixed(2) : '0.00'

    return `FARM FINANCIAL REPORT
=====================
Generated: ${new Date().toLocaleDateString('fr-FR')}
Period: ${selectedPeriod.toUpperCase()}
Report Type: Financial Analysis

=== EXECUTIVE SUMMARY ===
Total Farms: ${reportData.farms.length}
Total Revenue: ${formatCurrency(reportData.totalRevenue)}
Total Expenses: ${formatCurrency(reportData.totalExpenses)}
Net Profit: ${formatCurrency(reportData.totalProfit)}
Profit Margin: ${profitMargin}%

=== FARM BREAKDOWN ===
${reportData.farms.map(farm => {
  const profit = farm.totalRevenue - farm.totalExpenses
  const margin = farm.totalRevenue > 0 ? ((profit / farm.totalRevenue) * 100).toFixed(1) : '0.0'

  return `
${farm.name}
Location: ${farm.location}
Revenue: ${formatCurrency(farm.totalRevenue)}
Expenses: ${formatCurrency(farm.totalExpenses)}
Profit: ${formatCurrency(profit)}
Margin: ${margin}%
Employees: ${farm.employees}
Fields: ${farm.fields}
Crops: ${farm.crops.join(', ')}
---`
}).join('')}

=== PERFORMANCE ANALYSIS ===
Best Performing Farm: ${reportData.farms.reduce((best, farm) =>
  (farm.totalRevenue - farm.totalExpenses) > (best.totalRevenue - best.totalExpenses) ? farm : best
).name}

Highest Revenue: ${reportData.farms.reduce((highest, farm) =>
  farm.totalRevenue > highest.totalRevenue ? farm : highest
).name}

=== RECOMMENDATIONS ===
- Focus investment on high-margin farms
- Review cost structure for underperforming operations
- Consider scaling successful farm models
- Implement cost reduction strategies where needed
- Explore new revenue streams for existing farms

=== END OF REPORT ===
Generated by Farm Management System
Contact: <EMAIL>
`
  }

  const generateOperationalReport = () => {
    if (!reportData) return ''

    const avgFieldsPerFarm = (reportData.totalFields / reportData.farms.length).toFixed(1)
    const avgEmployeesPerFarm = (reportData.totalEmployees / reportData.farms.length).toFixed(1)

    return `FARM OPERATIONAL REPORT
=======================
Generated: ${new Date().toLocaleDateString('fr-FR')}
Period: ${selectedPeriod.toUpperCase()}
Report Type: Operational Analysis

=== OPERATIONAL SUMMARY ===
Total Farms: ${reportData.farms.length}
Total Fields: ${reportData.totalFields}
Total Employees: ${reportData.totalEmployees}
Unique Crop Varieties: ${reportData.totalCrops}
Average Fields per Farm: ${avgFieldsPerFarm}
Average Employees per Farm: ${avgEmployeesPerFarm}

=== FARM OPERATIONAL DETAILS ===
${reportData.farms.map((farm, index) => `
${index + 1}. ${farm.name}
   Location: ${farm.location}
   Operational Scale:
   - Fields: ${farm.fields}
   - Employees: ${farm.employees}
   - Crop Varieties: ${farm.crops.length}

   Crop Portfolio:
   ${farm.crops.map(crop => `   • ${crop}`).join('\n')}

   Efficiency Metrics:
   - Revenue per Employee: ${farm.employees > 0 ?
     new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'XAF', minimumFractionDigits: 0 })
       .format(farm.totalRevenue / farm.employees) : 'N/A'}
   - Revenue per Field: ${farm.fields > 0 ?
     new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'XAF', minimumFractionDigits: 0 })
       .format(farm.totalRevenue / farm.fields) : 'N/A'}

   ---`).join('')}

=== OPERATIONAL INSIGHTS ===
Largest Farm by Fields: ${reportData.farms.reduce((largest, farm) =>
  farm.fields > largest.fields ? farm : largest
).name} (${reportData.farms.reduce((largest, farm) =>
  farm.fields > largest.fields ? farm : largest
).fields} fields)

Most Employees: ${reportData.farms.reduce((most, farm) =>
  farm.employees > most.employees ? farm : most
).name} (${reportData.farms.reduce((most, farm) =>
  farm.employees > most.employees ? farm : most
).employees} employees)

Most Diverse Crops: ${reportData.farms.reduce((most, farm) =>
  farm.crops.length > most.crops.length ? farm : most
).name} (${reportData.farms.reduce((most, farm) =>
  farm.crops.length > most.crops.length ? farm : most
).crops.length} varieties)

=== RECOMMENDATIONS ===
- Optimize field utilization across all farms
- Consider cross-training employees for flexibility
- Explore crop diversification opportunities
- Implement best practices from top-performing farms
- Review staffing levels for operational efficiency

=== END OF REPORT ===
Generated by Farm Management System
Contact: <EMAIL>
`
  }

  const generateProductivityReport = () => {
    if (!reportData) return ''

    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: 'XAF',
        minimumFractionDigits: 0
      }).format(amount)
    }

    const avgRevenuePerFarm = reportData.totalRevenue / reportData.farms.length
    const avgRevenuePerEmployee = reportData.totalRevenue / reportData.totalEmployees
    const avgFieldsPerFarm = reportData.totalFields / reportData.farms.length

    return `FARM PRODUCTIVITY REPORT
========================
Generated: ${new Date().toLocaleDateString('fr-FR')}
Period: ${selectedPeriod.toUpperCase()}
Report Type: Productivity Analysis

=== PRODUCTIVITY OVERVIEW ===
Total Farms Analyzed: ${reportData.farms.length}
Combined Revenue: ${formatCurrency(reportData.totalRevenue)}
Combined Profit: ${formatCurrency(reportData.totalProfit)}

=== KEY PRODUCTIVITY METRICS ===
Average Revenue per Farm: ${formatCurrency(avgRevenuePerFarm)}
Average Revenue per Employee: ${formatCurrency(avgRevenuePerEmployee)}
Average Revenue per Field: ${formatCurrency(reportData.totalRevenue / reportData.totalFields)}
Average Fields per Farm: ${avgFieldsPerFarm.toFixed(1)}
Average Employees per Farm: ${(reportData.totalEmployees / reportData.farms.length).toFixed(1)}

=== INDIVIDUAL FARM PERFORMANCE ===
${reportData.farms.map((farm, index) => {
  const revenuePerEmployee = farm.employees > 0 ? farm.totalRevenue / farm.employees : 0
  const revenuePerField = farm.fields > 0 ? farm.totalRevenue / farm.fields : 0
  const profit = farm.totalRevenue - farm.totalExpenses
  const profitMargin = farm.totalRevenue > 0 ? (profit / farm.totalRevenue) * 100 : 0

  // Calculate efficiency score (0-100 scale)
  const employeeEfficiency = revenuePerEmployee / avgRevenuePerEmployee * 100
  const fieldEfficiency = revenuePerField / (reportData.totalRevenue / reportData.totalFields) * 100
  const overallEfficiency = ((employeeEfficiency + fieldEfficiency) / 2).toFixed(1)

  return `
${index + 1}. ${farm.name} (${farm.location})
   Financial Performance:
   - Total Revenue: ${formatCurrency(farm.totalRevenue)}
   - Total Profit: ${formatCurrency(profit)}
   - Profit Margin: ${profitMargin.toFixed(1)}%

   Productivity Metrics:
   - Revenue per Employee: ${formatCurrency(revenuePerEmployee)}
   - Revenue per Field: ${formatCurrency(revenuePerField)}
   - Employee Efficiency: ${employeeEfficiency.toFixed(1)}% of average
   - Field Efficiency: ${fieldEfficiency.toFixed(1)}% of average
   - Overall Efficiency Score: ${overallEfficiency}%

   Resource Utilization:
   - Employees: ${farm.employees}
   - Fields: ${farm.fields}
   - Crop Varieties: ${farm.crops.length}
   - Crops: ${farm.crops.join(', ')}

   ---`
}).join('')}

=== PRODUCTIVITY RANKINGS ===

Top Performers by Revenue:
${reportData.farms
  .sort((a, b) => b.totalRevenue - a.totalRevenue)
  .slice(0, 3)
  .map((farm, index) => `${index + 1}. ${farm.name} - ${formatCurrency(farm.totalRevenue)}`)
  .join('\n')}

Top Performers by Profit:
${reportData.farms
  .sort((a, b) => (b.totalRevenue - b.totalExpenses) - (a.totalRevenue - a.totalExpenses))
  .slice(0, 3)
  .map((farm, index) => {
    const profit = farm.totalRevenue - farm.totalExpenses
    return `${index + 1}. ${farm.name} - ${formatCurrency(profit)}`
  })
  .join('\n')}

Most Efficient by Employee:
${reportData.farms
  .filter(farm => farm.employees > 0)
  .sort((a, b) => (b.totalRevenue / b.employees) - (a.totalRevenue / a.employees))
  .slice(0, 3)
  .map((farm, index) => `${index + 1}. ${farm.name} - ${formatCurrency(farm.totalRevenue / farm.employees)}/employee`)
  .join('\n')}

=== RECOMMENDATIONS ===
- Benchmark underperforming farms against top performers
- Implement productivity improvement programs
- Consider resource reallocation for optimal efficiency
- Focus on high-margin crop varieties
- Invest in training and technology for productivity gains
- Review and optimize operational processes

=== PRODUCTIVITY IMPROVEMENT OPPORTUNITIES ===
- Farms below average efficiency should adopt best practices
- Consider consolidating or expanding based on performance metrics
- Implement performance monitoring systems
- Regular productivity reviews and benchmarking

=== END OF REPORT ===
Generated by Farm Management System
Contact: <EMAIL>
`
  }

  const generateCSVReport = () => {
    if (!reportData) {
      alert('No data available for CSV export.')
      return
    }

    try {
      // Create CSV content
      const headers = ['Farm Name', 'Location', 'Revenue (CFA)', 'Expenses (CFA)', 'Profit (CFA)', 'Profit Margin (%)', 'Employees', 'Fields', 'Crops']
      const csvContent = [
        headers.join(','),
        ...reportData.farms.map(farm => {
          const profit = farm.totalRevenue - farm.totalExpenses
          const profitMargin = farm.totalRevenue > 0 ? ((profit / farm.totalRevenue) * 100).toFixed(2) : '0.00'

          return [
            `"${farm.name}"`,
            `"${farm.location}"`,
            farm.totalRevenue,
            farm.totalExpenses,
            profit,
            profitMargin,
            farm.employees,
            farm.fields,
            `"${farm.crops.join('; ')}"`
          ].join(',')
        })
      ].join('\n')

      // Download CSV
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `farm-data-export-${new Date().toISOString().split('T')[0]}.csv`
      a.style.display = 'none'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      alert('✅ CSV report exported successfully!')
    } catch (error) {
      console.error('Error generating CSV:', error)
      alert('Error generating CSV export. Please try again.')
    }
  }

  const scheduleReport = () => {
    alert(`📅 Report scheduled for ${selectedPeriod} delivery.\n\nYou will receive email notifications when reports are ready.\n\nScheduled reports will include:\n• Financial analysis\n• Operational metrics\n• Performance trends`)
  }

  if (loading) {
    return (
      <div className="farm-bg-reports min-h-screen -m-6 p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading report data...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="farm-bg-reports min-h-screen -m-6 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <FileText className="mr-3 h-8 w-8 text-green-600" />
                Farm Reports & Analytics
              </h1>
              <p className="mt-2 text-gray-600">
                Generate comprehensive reports and analyze farm performance across all operations
              </p>
            </div>
            <button
              onClick={fetchReportData}
              className="btn-secondary flex items-center"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh Data
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <Filter className="h-5 w-5 text-gray-500" />
            <h3 className="text-lg font-medium text-gray-900">Report Filters</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Report Period
              </label>
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="input-field"
              >
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
                <option value="quarterly">Quarterly</option>
                <option value="yearly">Yearly</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Farm Selection
              </label>
              <select
                value={selectedFarm}
                onChange={(e) => setSelectedFarm(e.target.value)}
                className="input-field"
              >
                <option value="all">All Farms</option>
                {reportData?.farms.map(farm => (
                  <option key={farm.id} value={farm.id}>{farm.name}</option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Report Type
              </label>
              <select
                value={reportType}
                onChange={(e) => setReportType(e.target.value)}
                className="input-field"
              >
                <option value="financial">Financial Report</option>
                <option value="operational">Operational Report</option>
                <option value="productivity">Productivity Report</option>
              </select>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        {reportData && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DollarSign className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {new Intl.NumberFormat('fr-FR', {
                      style: 'currency',
                      currency: 'XAF',
                      minimumFractionDigits: 0
                    }).format(reportData.totalRevenue)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TrendingUp className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Net Profit</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {new Intl.NumberFormat('fr-FR', {
                      style: 'currency',
                      currency: 'XAF',
                      minimumFractionDigits: 0
                    }).format(reportData.totalProfit)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Users className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Employees</p>
                  <p className="text-2xl font-bold text-gray-900">{reportData.totalEmployees}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Sprout className="h-8 w-8 text-green-500" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Active Fields</p>
                  <p className="text-2xl font-bold text-gray-900">{reportData.totalFields}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Report Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Generate Reports */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <Download className="mr-2 h-5 w-5 text-green-600" />
              Generate Reports
            </h3>

            <div className="space-y-4">
              <button
                onClick={() => generateReport('financial')}
                disabled={generating || loading}
                className={`w-full flex items-center justify-center ${
                  generating || loading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'btn-primary hover:bg-green-700'
                }`}
              >
                {generating ? (
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <BarChart3 className="mr-2 h-4 w-4" />
                )}
                {generating ? 'Generating...' : 'Financial Report'}
              </button>

              <button
                onClick={() => generateReport('operational')}
                disabled={generating || loading}
                className={`w-full flex items-center justify-center ${
                  generating || loading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'btn-secondary hover:bg-gray-700'
                }`}
              >
                {generating ? (
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <MapPin className="mr-2 h-4 w-4" />
                )}
                {generating ? 'Generating...' : 'Operational Report'}
              </button>

              <button
                onClick={() => generateReport('productivity')}
                disabled={generating || loading}
                className={`w-full flex items-center justify-center ${
                  generating || loading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'btn-secondary hover:bg-gray-700'
                }`}
              >
                {generating ? (
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <PieChart className="mr-2 h-4 w-4" />
                )}
                {generating ? 'Generating...' : 'Productivity Report'}
              </button>

              <button
                onClick={generateCSVReport}
                disabled={generating || loading}
                className={`w-full font-medium py-2 px-4 rounded-md transition-colors flex items-center justify-center ${
                  generating || loading
                    ? 'bg-gray-400 cursor-not-allowed text-gray-200'
                    : 'bg-green-600 hover:bg-green-700 text-white'
                }`}
              >
                {generating ? (
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Download className="mr-2 h-4 w-4" />
                )}
                {generating ? 'Exporting...' : 'Export to CSV'}
              </button>
            </div>
          </div>

          {/* Schedule Reports */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <Calendar className="mr-2 h-5 w-5 text-blue-600" />
              Schedule Reports
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Delivery Frequency
                </label>
                <select className="input-field">
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Recipients
                </label>
                <input
                  type="email"
                  placeholder="<EMAIL>"
                  className="input-field"
                />
              </div>

              <button
                onClick={scheduleReport}
                className="w-full btn-primary flex items-center justify-center"
              >
                <Calendar className="mr-2 h-4 w-4" />
                Schedule Report
              </button>
            </div>
          </div>
        </div>

        {/* Farm Performance Table */}
        {reportData && (
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Farm Performance Overview</h3>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Farm Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Location
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Revenue
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Profit
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Employees
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Fields
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {reportData.farms.map((farm) => {
                    const profit = farm.totalRevenue - farm.totalExpenses
                    const profitMargin = farm.totalRevenue > 0 ? (profit / farm.totalRevenue) * 100 : 0

                    return (
                      <tr key={farm.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{farm.name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500">{farm.location}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {new Intl.NumberFormat('fr-FR', {
                              style: 'currency',
                              currency: 'XAF',
                              minimumFractionDigits: 0
                            }).format(farm.totalRevenue)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className={`text-sm ${profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {new Intl.NumberFormat('fr-FR', {
                              style: 'currency',
                              currency: 'XAF',
                              minimumFractionDigits: 0
                            }).format(profit)}
                            <span className="text-xs ml-1">
                              ({profitMargin.toFixed(1)}%)
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{farm.employees}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{farm.fields}</div>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
