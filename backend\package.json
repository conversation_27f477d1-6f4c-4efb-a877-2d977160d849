{"name": "farm-app-backend", "version": "1.0.0", "description": "Backend API for Farm App", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "db:seed": "tsx src/database/seed.ts"}, "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/mongoose": "^5.11.97", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.9.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsx": "^4.1.4", "typescript": "^5.2.2"}, "keywords": ["farm-management", "agriculture", "api", "nodejs", "express"], "author": "Farm Management Team", "license": "MIT"}