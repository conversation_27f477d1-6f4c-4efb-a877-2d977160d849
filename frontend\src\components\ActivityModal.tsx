import { useState, useEffect } from 'react'
import { X } from 'lucide-react'
import { Activity, CreateActivityData, UpdateActivityData } from '../services/activityService'

interface ActivityModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: CreateActivityData | UpdateActivityData) => Promise<void>
  activity?: Activity | null
  loading?: boolean
}

export default function ActivityModal({ isOpen, onClose, onSubmit, activity, loading }: ActivityModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: 'Planting',
    fieldId: 1,
    status: 'Scheduled',
    priority: 'Medium',
    scheduledDate: '',
    assignedTo: '',
    estimatedHours: 1,
    cost: 0,
    notes: '',
  })

  useEffect(() => {
    if (activity) {
      setFormData({
        title: activity.title,
        description: activity.description,
        type: activity.type,
        fieldId: activity.fieldId,
        status: activity.status,
        priority: activity.priority,
        scheduledDate: activity.scheduledDate,
        assignedTo: activity.assignedTo || '',
        estimatedHours: activity.estimatedHours,
        cost: activity.cost || 0,
        notes: activity.notes || '',
      })
    } else {
      setFormData({
        title: '',
        description: '',
        type: 'Planting',
        fieldId: 1,
        status: 'Scheduled',
        priority: 'Medium',
        scheduledDate: '',
        assignedTo: '',
        estimatedHours: 1,
        cost: 0,
        notes: '',
      })
    }
  }, [activity])

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    try {
      await onSubmit(formData)
      onClose()
    } catch (error) {
      console.error('Error submitting activity:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: name === 'fieldId' || name === 'estimatedHours' || name === 'cost' ? Number(value) : value
    }))
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={onClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <h3 className="text-lg font-medium text-gray-900">
              {activity ? 'Edit Activity' : 'Schedule New Activity'}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(handleFormSubmit)} className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                  Activity Title *
                </label>
                <input
                  {...register('title', { required: 'Title is required' })}
                  type="text"
                  className="input mt-1"
                  placeholder="Enter activity title"
                />
                {errors.title && (
                  <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                  Activity Type *
                </label>
                <select
                  {...register('type', { required: 'Type is required' })}
                  className="input mt-1"
                >
                  <option value="Planting">Planting</option>
                  <option value="Irrigation">Irrigation</option>
                  <option value="Fertilization">Fertilization</option>
                  <option value="Pest Control">Pest Control</option>
                  <option value="Harvesting">Harvesting</option>
                  <option value="Maintenance">Maintenance</option>
                </select>
                {errors.type && (
                  <p className="mt-1 text-sm text-red-600">{errors.type.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="fieldId" className="block text-sm font-medium text-gray-700">
                  Field *
                </label>
                <select
                  {...register('fieldId', { required: 'Field is required' })}
                  className="input mt-1"
                >
                  <option value={1}>North Field A</option>
                  <option value={2}>South Field B</option>
                  <option value={3}>East Field C</option>
                  <option value={0}>Equipment Yard</option>
                </select>
                {errors.fieldId && (
                  <p className="mt-1 text-sm text-red-600">{errors.fieldId.message}</p>
                )}
              </div>
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description *
              </label>
              <textarea
                {...register('description', { required: 'Description is required' })}
                rows={3}
                className="input mt-1"
                placeholder="Describe the activity in detail"
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
              )}
            </div>

            {/* Scheduling */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="scheduledDate" className="block text-sm font-medium text-gray-700">
                  Scheduled Date *
                </label>
                <input
                  {...register('scheduledDate', { required: 'Date is required' })}
                  type="date"
                  className="input mt-1"
                />
                {errors.scheduledDate && (
                  <p className="mt-1 text-sm text-red-600">{errors.scheduledDate.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                  Status
                </label>
                <select
                  {...register('status')}
                  className="input mt-1"
                >
                  <option value="Scheduled">Scheduled</option>
                  <option value="In Progress">In Progress</option>
                  <option value="Completed">Completed</option>
                  <option value="Cancelled">Cancelled</option>
                </select>
              </div>

              <div>
                <label htmlFor="priority" className="block text-sm font-medium text-gray-700">
                  Priority
                </label>
                <select
                  {...register('priority')}
                  className="input mt-1"
                >
                  <option value="Low">Low</option>
                  <option value="Medium">Medium</option>
                  <option value="High">High</option>
                </select>
              </div>
            </div>

            {/* Assignment and Resources */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="assignedTo" className="block text-sm font-medium text-gray-700">
                  Assigned To
                </label>
                <input
                  {...register('assignedTo')}
                  type="text"
                  className="input mt-1"
                  placeholder="Person responsible"
                />
              </div>

              <div>
                <label htmlFor="estimatedHours" className="block text-sm font-medium text-gray-700">
                  Estimated Hours *
                </label>
                <input
                  {...register('estimatedHours', { 
                    required: 'Hours required',
                    min: { value: 0.5, message: 'Minimum 0.5 hours' }
                  })}
                  type="number"
                  step="0.5"
                  className="input mt-1"
                  placeholder="0"
                />
                {errors.estimatedHours && (
                  <p className="mt-1 text-sm text-red-600">{errors.estimatedHours.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="cost" className="block text-sm font-medium text-gray-700">
                  Estimated Cost ($)
                </label>
                <input
                  {...register('cost', { 
                    min: { value: 0, message: 'Cost must be positive' }
                  })}
                  type="number"
                  step="0.01"
                  className="input mt-1"
                  placeholder="0.00"
                />
                {errors.cost && (
                  <p className="mt-1 text-sm text-red-600">{errors.cost.message}</p>
                )}
              </div>
            </div>

            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                Notes
              </label>
              <textarea
                {...register('notes')}
                rows={3}
                className="input mt-1"
                placeholder="Additional notes or instructions"
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t">
              <button
                type="button"
                onClick={onClose}
                className="btn-outline"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn-primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : activity ? 'Update Activity' : 'Schedule Activity'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
