import api from './api'

export interface Activity {
  id: number
  title: string
  description: string
  type: 'Planting' | 'Irrigation' | 'Fertilization' | 'Pest Control' | 'Harvesting' | 'Maintenance'
  fieldId: number
  fieldName: string
  farmName: string
  status: 'Scheduled' | 'In Progress' | 'Completed' | 'Cancelled'
  priority: 'Low' | 'Medium' | 'High'
  scheduledDate: string
  completedDate?: string
  assignedTo?: string
  estimatedHours: number
  actualHours?: number
  cost?: number
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface CreateActivityData {
  title: string
  description: string
  type: 'Planting' | 'Irrigation' | 'Fertilization' | 'Pest Control' | 'Harvesting' | 'Maintenance'
  fieldId: number
  status: 'Scheduled' | 'In Progress' | 'Completed' | 'Cancelled'
  priority: 'Low' | 'Medium' | 'High'
  scheduledDate: string
  assignedTo?: string
  estimatedHours: number
  cost?: number
  notes?: string
}

export interface UpdateActivityData {
  title?: string
  description?: string
  type?: 'Planting' | 'Irrigation' | 'Fertilization' | 'Pest Control' | 'Harvesting' | 'Maintenance'
  fieldId?: number
  status?: 'Scheduled' | 'In Progress' | 'Completed' | 'Cancelled'
  priority?: 'Low' | 'Medium' | 'High'
  scheduledDate?: string
  completedDate?: string
  assignedTo?: string
  estimatedHours?: number
  actualHours?: number
  cost?: number
  notes?: string
}

// Mock data for demo purposes
let mockActivities: Activity[] = [
  {
    id: 1,
    title: 'Corn Field Irrigation',
    description: 'Weekly irrigation for corn field to maintain optimal soil moisture',
    type: 'Irrigation',
    fieldId: 1,
    fieldName: 'North Field A',
    farmName: 'Green Valley Farm',
    status: 'Scheduled',
    priority: 'High',
    scheduledDate: '2024-07-05',
    assignedTo: 'John Martinez',
    estimatedHours: 4,
    cost: 90000, // 150 USD = ~90k CFA
    notes: 'Check irrigation system pressure before starting',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 2,
    title: 'Wheat Harvest',
    description: 'Final harvest of winter wheat crop',
    type: 'Harvesting',
    fieldId: 2,
    fieldName: 'South Field B',
    farmName: 'Green Valley Farm',
    status: 'Completed',
    priority: 'High',
    scheduledDate: '2024-07-18',
    completedDate: '2024-07-18',
    assignedTo: 'Sarah Johnson',
    estimatedHours: 12,
    actualHours: 14,
    cost: 480000, // 800 USD = ~480k CFA
    notes: 'Excellent yield achieved. Equipment performed well.',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 3,
    title: 'Cotton Pest Control',
    description: 'Apply organic pesticide to control bollworm infestation',
    type: 'Pest Control',
    fieldId: 3,
    fieldName: 'East Field C',
    farmName: 'Sunrise Agriculture',
    status: 'In Progress',
    priority: 'High',
    scheduledDate: '2024-07-02',
    assignedTo: 'Mike Rodriguez',
    estimatedHours: 6,
    actualHours: 4,
    cost: 192000, // 320 USD = ~192k CFA
    notes: 'Weather conditions favorable for application',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 4,
    title: 'Fertilizer Application',
    description: 'Apply nitrogen fertilizer to soybeans for optimal growth',
    type: 'Fertilization',
    fieldId: 1,
    fieldName: 'North Field A',
    farmName: 'Green Valley Farm',
    status: 'Scheduled',
    priority: 'Medium',
    scheduledDate: '2024-07-08',
    assignedTo: 'Lisa Chen',
    estimatedHours: 3,
    cost: 270000, // 450 USD = ~270k CFA
    notes: 'Use precision application equipment',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 5,
    title: 'Equipment Maintenance',
    description: 'Routine maintenance on combine harvester',
    type: 'Maintenance',
    fieldId: 0,
    fieldName: 'Equipment Yard',
    farmName: 'Green Valley Farm',
    status: 'Scheduled',
    priority: 'Medium',
    scheduledDate: '2024-07-10',
    assignedTo: 'Tom Wilson',
    estimatedHours: 8,
    cost: 150000, // 250 USD = ~150k CFA
    notes: 'Replace filters and check hydraulic fluid levels',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 6,
    title: 'Soybean Planting',
    description: 'Plant soybeans in prepared field sections',
    type: 'Planting',
    fieldId: 1,
    fieldName: 'North Field A',
    farmName: 'Green Valley Farm',
    status: 'Completed',
    priority: 'High',
    scheduledDate: '2024-05-20',
    completedDate: '2024-05-22',
    assignedTo: 'John Martinez',
    estimatedHours: 10,
    actualHours: 12,
    cost: 360000, // 600 USD = ~360k CFA
    notes: 'Planting completed successfully. Good soil conditions.',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
]

export const activityService = {
  // Get all activities
  async getActivities(): Promise<Activity[]> {
    await new Promise(resolve => setTimeout(resolve, 500))
    return [...mockActivities]
  },

  // Get activity by ID
  async getActivity(id: number): Promise<Activity> {
    await new Promise(resolve => setTimeout(resolve, 300))
    const activity = mockActivities.find(a => a.id === id)
    if (!activity) throw new Error('Activity not found')
    return activity
  },

  // Create new activity
  async createActivity(data: CreateActivityData): Promise<Activity> {
    await new Promise(resolve => setTimeout(resolve, 800))
    const newActivity: Activity = {
      id: Math.max(...mockActivities.map(a => a.id)) + 1,
      ...data,
      fieldName: 'Field Name', // Mock field name
      farmName: 'Farm Name', // Mock farm name
      completedDate: undefined,
      actualHours: undefined,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    mockActivities.push(newActivity)
    return newActivity
  },

  // Update activity
  async updateActivity(id: number, data: UpdateActivityData): Promise<Activity> {
    await new Promise(resolve => setTimeout(resolve, 800))
    const index = mockActivities.findIndex(a => a.id === id)
    if (index === -1) throw new Error('Activity not found')

    mockActivities[index] = {
      ...mockActivities[index],
      ...data,
      updatedAt: new Date().toISOString()
    }
    return mockActivities[index]
  },

  // Delete activity
  async deleteActivity(id: number): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 500))
    const index = mockActivities.findIndex(a => a.id === id)
    if (index === -1) throw new Error('Activity not found')
    mockActivities.splice(index, 1)
  },
}
