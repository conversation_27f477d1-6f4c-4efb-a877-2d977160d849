// Security Service for Farm App
import CryptoJS from 'crypto-js'

export interface SecurityConfig {
  maxLoginAttempts: number
  lockoutDuration: number // in minutes
  sessionTimeout: number // in minutes
  passwordMinLength: number
  requireSpecialChars: boolean
  requireNumbers: boolean
  requireUppercase: boolean
}

export interface LoginAttempt {
  email: string
  timestamp: number
  success: boolean
  ipAddress?: string
}

export interface SecurityEvent {
  id: string
  type: 'login' | 'logout' | 'failed_login' | 'password_change' | 'data_access' | 'suspicious_activity'
  userId?: string
  email?: string
  timestamp: number
  details: string
  ipAddress?: string
  userAgent?: string
}

class SecurityService {
  private readonly config: SecurityConfig = {
    maxLoginAttempts: 5,
    lockoutDuration: 30, // 30 minutes
    sessionTimeout: 120, // 2 hours
    passwordMinLength: 8,
    requireSpecialChars: true,
    requireNumbers: true,
    requireUppercase: true
  }

  private readonly encryptionKey = 'farm-app-security-key-2024'

  // Password validation
  validatePassword(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (password.length < this.config.passwordMinLength) {
      errors.push(`Password must be at least ${this.config.passwordMinLength} characters long`)
    }

    if (this.config.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter')
    }

    if (this.config.requireNumbers && !/\d/.test(password)) {
      errors.push('Password must contain at least one number')
    }

    if (this.config.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  // Email validation
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // Check if account is locked due to failed attempts
  isAccountLocked(email: string): boolean {
    const attempts = this.getLoginAttempts(email)
    const recentFailedAttempts = attempts.filter(attempt => 
      !attempt.success && 
      Date.now() - attempt.timestamp < this.config.lockoutDuration * 60 * 1000
    )

    return recentFailedAttempts.length >= this.config.maxLoginAttempts
  }

  // Record login attempt
  recordLoginAttempt(email: string, success: boolean): void {
    const attempts = this.getLoginAttempts(email)
    const newAttempt: LoginAttempt = {
      email,
      timestamp: Date.now(),
      success,
      ipAddress: this.getClientIP()
    }

    attempts.push(newAttempt)
    
    // Keep only recent attempts (last 24 hours)
    const filtered = attempts.filter(attempt => 
      Date.now() - attempt.timestamp < 24 * 60 * 60 * 1000
    )

    localStorage.setItem(`login_attempts_${email}`, JSON.stringify(filtered))
    
    // Log security event
    this.logSecurityEvent({
      type: success ? 'login' : 'failed_login',
      email,
      details: success ? 'Successful login' : 'Failed login attempt',
      ipAddress: this.getClientIP()
    })
  }

  // Get login attempts for email
  private getLoginAttempts(email: string): LoginAttempt[] {
    const stored = localStorage.getItem(`login_attempts_${email}`)
    return stored ? JSON.parse(stored) : []
  }

  // Get client IP (simulated for demo)
  private getClientIP(): string {
    // In a real app, this would be provided by the backend
    return '*************'
  }

  // Session management
  isSessionValid(): boolean {
    const sessionStart = localStorage.getItem('session_start')
    if (!sessionStart) return false

    const elapsed = Date.now() - parseInt(sessionStart)
    return elapsed < this.config.sessionTimeout * 60 * 1000
  }

  refreshSession(): void {
    localStorage.setItem('session_start', Date.now().toString())
  }

  // Data encryption/decryption
  encryptData(data: string): string {
    return CryptoJS.AES.encrypt(data, this.encryptionKey).toString()
  }

  decryptData(encryptedData: string): string {
    const bytes = CryptoJS.AES.decrypt(encryptedData, this.encryptionKey)
    return bytes.toString(CryptoJS.enc.Utf8)
  }

  // Security event logging
  logSecurityEvent(event: Omit<SecurityEvent, 'id' | 'timestamp' | 'userAgent'>): void {
    const securityEvent: SecurityEvent = {
      id: this.generateId(),
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      ...event
    }

    const events = this.getSecurityEvents()
    events.push(securityEvent)
    
    // Keep only last 1000 events
    if (events.length > 1000) {
      events.splice(0, events.length - 1000)
    }

    localStorage.setItem('security_events', JSON.stringify(events))
  }

  // Get security events
  getSecurityEvents(): SecurityEvent[] {
    const stored = localStorage.getItem('security_events')
    return stored ? JSON.parse(stored) : []
  }

  // Get recent security events (last 24 hours)
  getRecentSecurityEvents(): SecurityEvent[] {
    const events = this.getSecurityEvents()
    const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000
    return events.filter(event => event.timestamp > oneDayAgo)
  }

  // Generate unique ID
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // Clear security data (for logout)
  clearSecurityData(): void {
    localStorage.removeItem('session_start')
    localStorage.removeItem('auth_token')
    
    this.logSecurityEvent({
      type: 'logout',
      details: 'User logged out'
    })
  }

  // Check for suspicious activity
  detectSuspiciousActivity(): { suspicious: boolean; reasons: string[] } {
    const events = this.getRecentSecurityEvents()
    const reasons: string[] = []

    // Check for multiple failed logins
    const failedLogins = events.filter(e => e.type === 'failed_login')
    if (failedLogins.length > 10) {
      reasons.push('Multiple failed login attempts detected')
    }

    // Check for rapid successive logins from different locations (simulated)
    const logins = events.filter(e => e.type === 'login')
    if (logins.length > 5) {
      reasons.push('Unusual login frequency detected')
    }

    return {
      suspicious: reasons.length > 0,
      reasons
    }
  }

  // Get security summary
  getSecuritySummary() {
    const events = this.getRecentSecurityEvents()
    const suspicious = this.detectSuspiciousActivity()

    return {
      totalEvents: events.length,
      loginAttempts: events.filter(e => e.type === 'login').length,
      failedLogins: events.filter(e => e.type === 'failed_login').length,
      suspiciousActivity: suspicious.suspicious,
      suspiciousReasons: suspicious.reasons,
      lastActivity: events.length > 0 ? new Date(Math.max(...events.map(e => e.timestamp))) : null
    }
  }

  // Data sanitization
  sanitizeInput(input: string): string {
    return input
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/['"]/g, '') // Remove quotes
      .trim()
  }

  // Rate limiting check
  checkRateLimit(action: string, maxAttempts: number = 10, timeWindow: number = 60): boolean {
    const key = `rate_limit_${action}`
    const attempts = JSON.parse(localStorage.getItem(key) || '[]')
    const now = Date.now()
    const windowStart = now - timeWindow * 1000

    // Filter recent attempts
    const recentAttempts = attempts.filter((timestamp: number) => timestamp > windowStart)
    
    if (recentAttempts.length >= maxAttempts) {
      return false // Rate limit exceeded
    }

    // Record this attempt
    recentAttempts.push(now)
    localStorage.setItem(key, JSON.stringify(recentAttempts))
    
    return true // Within rate limit
  }
}

export const securityService = new SecurityService()
