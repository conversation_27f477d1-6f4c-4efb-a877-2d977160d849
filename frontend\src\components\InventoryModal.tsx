import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { X } from 'lucide-react'
import { InventoryItem, CreateInventoryItemData, UpdateInventoryItemData } from '../services/inventoryService'

interface InventoryModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: CreateInventoryItemData | UpdateInventoryItemData) => Promise<void>
  item?: InventoryItem | null
  loading?: boolean
}

export default function InventoryModal({ isOpen, onClose, onSubmit, item, loading }: InventoryModalProps) {
  const { register, handleSubmit, formState: { errors }, reset, watch } = useForm<CreateInventoryItemData>()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const quantity = watch('quantity')
  const unitPrice = watch('unitPrice')

  useEffect(() => {
    if (item) {
      reset({
        name: item.name,
        category: item.category,
        description: item.description,
        quantity: item.quantity,
        unit: item.unit,
        minStockLevel: item.minStockLevel,
        maxStockLevel: item.maxStockLevel,
        unitPrice: item.unitPrice,
        supplier: item.supplier || '',
        location: item.location || '',
        expiryDate: item.expiryDate || '',
      })
    } else {
      reset({
        name: '',
        category: 'Seeds',
        description: '',
        quantity: 0,
        unit: '',
        minStockLevel: 0,
        maxStockLevel: 1000,
        unitPrice: 0,
        supplier: '',
        location: '',
        expiryDate: '',
      })
    }
  }, [item, reset])

  const handleFormSubmit = async (data: CreateInventoryItemData) => {
    setIsSubmitting(true)
    try {
      await onSubmit(data)
      onClose()
      reset()
    } catch (error) {
      console.error('Error submitting inventory item:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!isOpen) return null

  const totalValue = (quantity || 0) * (unitPrice || 0)

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={onClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <h3 className="text-lg font-medium text-gray-900">
              {item ? 'Edit Inventory Item' : 'Add New Inventory Item'}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(handleFormSubmit)} className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Item Name *
                </label>
                <input
                  {...register('name', { required: 'Item name is required' })}
                  type="text"
                  className="input mt-1"
                  placeholder="Enter item name"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                  Category *
                </label>
                <select
                  {...register('category', { required: 'Category is required' })}
                  className="input mt-1"
                >
                  <option value="Seeds">Seeds</option>
                  <option value="Fertilizers">Fertilizers</option>
                  <option value="Pesticides">Pesticides</option>
                  <option value="Equipment">Equipment</option>
                  <option value="Tools">Tools</option>
                  <option value="Fuel">Fuel</option>
                  <option value="Other">Other</option>
                </select>
                {errors.category && (
                  <p className="mt-1 text-sm text-red-600">{errors.category.message}</p>
                )}
              </div>
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description *
              </label>
              <textarea
                {...register('description', { required: 'Description is required' })}
                rows={3}
                className="input mt-1"
                placeholder="Enter item description"
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
              )}
            </div>

            {/* Quantity and Unit */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">
                  Quantity *
                </label>
                <input
                  {...register('quantity', { 
                    required: 'Quantity is required',
                    min: { value: 0, message: 'Quantity must be 0 or greater' }
                  })}
                  type="number"
                  step="0.01"
                  className="input mt-1"
                  placeholder="0"
                />
                {errors.quantity && (
                  <p className="mt-1 text-sm text-red-600">{errors.quantity.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="unit" className="block text-sm font-medium text-gray-700">
                  Unit *
                </label>
                <input
                  {...register('unit', { required: 'Unit is required' })}
                  type="text"
                  className="input mt-1"
                  placeholder="kg, liters, pieces"
                />
                {errors.unit && (
                  <p className="mt-1 text-sm text-red-600">{errors.unit.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="unitPrice" className="block text-sm font-medium text-gray-700">
                  Unit Price (CFA) *
                </label>
                <input
                  {...register('unitPrice', { 
                    required: 'Unit price is required',
                    min: { value: 0, message: 'Price must be 0 or greater' }
                  })}
                  type="number"
                  step="0.01"
                  className="input mt-1"
                  placeholder="0.00"
                />
                {errors.unitPrice && (
                  <p className="mt-1 text-sm text-red-600">{errors.unitPrice.message}</p>
                )}
              </div>
            </div>

            {/* Stock Levels */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="minStockLevel" className="block text-sm font-medium text-gray-700">
                  Minimum Stock Level
                </label>
                <input
                  {...register('minStockLevel', { 
                    min: { value: 0, message: 'Must be 0 or greater' }
                  })}
                  type="number"
                  className="input mt-1"
                  placeholder="0"
                />
                {errors.minStockLevel && (
                  <p className="mt-1 text-sm text-red-600">{errors.minStockLevel.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="maxStockLevel" className="block text-sm font-medium text-gray-700">
                  Maximum Stock Level
                </label>
                <input
                  {...register('maxStockLevel', { 
                    min: { value: 1, message: 'Must be greater than 0' }
                  })}
                  type="number"
                  className="input mt-1"
                  placeholder="1000"
                />
                {errors.maxStockLevel && (
                  <p className="mt-1 text-sm text-red-600">{errors.maxStockLevel.message}</p>
                )}
              </div>
            </div>

            {/* Additional Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="supplier" className="block text-sm font-medium text-gray-700">
                  Supplier
                </label>
                <input
                  {...register('supplier')}
                  type="text"
                  className="input mt-1"
                  placeholder="Supplier name"
                />
              </div>

              <div>
                <label htmlFor="location" className="block text-sm font-medium text-gray-700">
                  Storage Location
                </label>
                <input
                  {...register('location')}
                  type="text"
                  className="input mt-1"
                  placeholder="Warehouse, section, etc."
                />
              </div>
            </div>

            <div>
              <label htmlFor="expiryDate" className="block text-sm font-medium text-gray-700">
                Expiry Date
              </label>
              <input
                {...register('expiryDate')}
                type="date"
                className="input mt-1"
              />
            </div>

            {/* Total Value Display */}
            {totalValue > 0 && (
              <div className="bg-gray-50 p-4 rounded-md">
                <div className="text-sm text-gray-600">Total Value</div>
                <div className="text-lg font-semibold text-gray-900">${totalValue.toFixed(2)}</div>
              </div>
            )}

            <div className="flex justify-end space-x-3 pt-4 border-t">
              <button
                type="button"
                onClick={onClose}
                className="btn-outline"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn-primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : item ? 'Update Item' : 'Add Item'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
