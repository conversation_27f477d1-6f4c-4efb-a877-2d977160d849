import api from './api'

export interface InventoryItem {
  id: number
  name: string
  category: 'Seeds' | 'Fertilizers' | 'Pesticides' | 'Equipment' | 'Tools' | 'Fuel' | 'Other'
  description: string
  quantity: number
  unit: string
  minStockLevel: number
  maxStockLevel: number
  unitPrice: number
  totalValue: number
  supplier?: string
  location?: string
  expiryDate?: string
  lastRestocked?: string
  status: 'In Stock' | 'Low Stock' | 'Out of Stock' | 'Expired'
  createdAt: string
  updatedAt: string
}

export interface CreateInventoryItemData {
  name: string
  category: 'Seeds' | 'Fertilizers' | 'Pesticides' | 'Equipment' | 'Tools' | 'Fuel' | 'Other'
  description: string
  quantity: number
  unit: string
  minStockLevel: number
  maxStockLevel: number
  unitPrice: number
  supplier?: string
  location?: string
  expiryDate?: string
}

export interface UpdateInventoryItemData {
  name?: string
  category?: 'Seeds' | 'Fertilizers' | 'Pesticides' | 'Equipment' | 'Tools' | 'Fuel' | 'Other'
  description?: string
  quantity?: number
  unit?: string
  minStockLevel?: number
  maxStockLevel?: number
  unitPrice?: number
  supplier?: string
  location?: string
  expiryDate?: string
  lastRestocked?: string
}

// Mock data for demo purposes
let mockInventory: InventoryItem[] = [
  {
    id: 1,
    name: 'Corn Seeds - Hybrid Variety',
    category: 'Seeds',
    description: 'High-yield hybrid corn seeds suitable for temperate climate',
    quantity: 500,
    unit: 'kg',
    minStockLevel: 100,
    maxStockLevel: 1000,
    unitPrice: 7500, // 12.50 USD = ~7,500 CFA per kg
    totalValue: 3750000, // 6,250 USD = ~3.75M CFA
    supplier: 'AgriSeeds Inc.',
    location: 'Warehouse A - Section 1',
    expiryDate: '2025-12-31',
    lastRestocked: '2024-02-15',
    status: 'In Stock',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 2,
    name: 'Nitrogen Fertilizer (NPK 20-10-10)',
    category: 'Fertilizers',
    description: 'Balanced NPK fertilizer for general crop nutrition',
    quantity: 50,
    unit: 'bags (50kg)',
    minStockLevel: 20,
    maxStockLevel: 200,
    unitPrice: 27000, // 45 USD = ~27,000 CFA per bag
    totalValue: 1350000, // 2,250 USD = ~1.35M CFA
    supplier: 'FertilizerCorp',
    location: 'Warehouse B - Section 2',
    expiryDate: '2026-06-30',
    lastRestocked: '2024-03-01',
    status: 'Low Stock',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 3,
    name: 'Glyphosate Herbicide',
    category: 'Pesticides',
    description: 'Broad-spectrum systemic herbicide',
    quantity: 0,
    unit: 'liters',
    minStockLevel: 10,
    maxStockLevel: 100,
    unitPrice: 15000, // 25 USD = ~15,000 CFA per liter
    totalValue: 0,
    supplier: 'ChemAg Solutions',
    location: 'Chemical Storage - Locked Cabinet',
    expiryDate: '2024-08-15',
    lastRestocked: '2023-12-10',
    status: 'Out of Stock',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
]

const calculateStatus = (quantity: number, minStockLevel: number): InventoryItem['status'] => {
  if (quantity === 0) return 'Out of Stock'
  if (quantity <= minStockLevel) return 'Low Stock'
  return 'In Stock'
}

export const inventoryService = {
  // Get all inventory items
  async getInventoryItems(): Promise<InventoryItem[]> {
    await new Promise(resolve => setTimeout(resolve, 500))
    return [...mockInventory]
  },

  // Get inventory item by ID
  async getInventoryItem(id: number): Promise<InventoryItem> {
    await new Promise(resolve => setTimeout(resolve, 300))
    const item = mockInventory.find(i => i.id === id)
    if (!item) throw new Error('Inventory item not found')
    return item
  },

  // Create new inventory item
  async createInventoryItem(data: CreateInventoryItemData): Promise<InventoryItem> {
    await new Promise(resolve => setTimeout(resolve, 800))
    const totalValue = data.quantity * data.unitPrice
    const status = calculateStatus(data.quantity, data.minStockLevel)

    const newItem: InventoryItem = {
      id: Math.max(...mockInventory.map(i => i.id)) + 1,
      ...data,
      totalValue,
      status,
      lastRestocked: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    mockInventory.push(newItem)
    return newItem
  },

  // Update inventory item
  async updateInventoryItem(id: number, data: UpdateInventoryItemData): Promise<InventoryItem> {
    await new Promise(resolve => setTimeout(resolve, 800))
    const index = mockInventory.findIndex(i => i.id === id)
    if (index === -1) throw new Error('Inventory item not found')

    const updatedItem = {
      ...mockInventory[index],
      ...data,
      updatedAt: new Date().toISOString()
    }

    // Recalculate values if quantity or price changed
    if (data.quantity !== undefined || data.unitPrice !== undefined) {
      updatedItem.totalValue = updatedItem.quantity * updatedItem.unitPrice
      updatedItem.status = calculateStatus(updatedItem.quantity, updatedItem.minStockLevel)
    }

    mockInventory[index] = updatedItem
    return updatedItem
  },

  // Delete inventory item
  async deleteInventoryItem(id: number): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 500))
    const index = mockInventory.findIndex(i => i.id === id)
    if (index === -1) throw new Error('Inventory item not found')
    mockInventory.splice(index, 1)
  },
}
