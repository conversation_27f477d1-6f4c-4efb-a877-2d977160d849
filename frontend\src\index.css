@tailwind base;
@tailwind components;
@tailwind utilities;

/* Farm-themed background images and colors */
.farm-bg-login {
  background-image: url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: relative;
}

.farm-bg-login::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.3);
  z-index: 1;
}

.farm-bg-login > * {
  position: relative;
  z-index: 2;
}

.farm-bg-dashboard {
  background: linear-gradient(135deg, #84cc16 0%, #65a30d 50%, #4d7c0f 100%);
  background-size: cover;
  background-position: center;
  position: relative;
}

.farm-bg-dashboard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  z-index: 1;
}

.farm-bg-dashboard > * {
  position: relative;
  z-index: 2;
}

.farm-bg-farms {
  background-image: url('https://images.unsplash.com/photo-1625246333195-78d9c38ad449?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: relative;
}

.farm-bg-farms::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.3);
  z-index: 1;
}

.farm-bg-farms > * {
  position: relative;
  z-index: 2;
}

.farm-bg-fields {
  background: linear-gradient(135deg, #a3e635 0%, #84cc16 50%, #65a30d 100%);
  background-size: cover;
  background-position: center;
  position: relative;
}

.farm-bg-fields::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  z-index: 1;
}

.farm-bg-fields > * {
  position: relative;
  z-index: 2;
}

.farm-bg-crops {
  background-image: url('https://images.unsplash.com/photo-1416879595882-3373a0480b5b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: relative;
}

.farm-bg-crops::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.3);
  z-index: 1;
}

.farm-bg-crops > * {
  position: relative;
  z-index: 2;
}

.farm-bg-finance {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
  background-size: cover;
  background-position: center;
  position: relative;
}

.farm-bg-finance::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  z-index: 1;
}

.farm-bg-finance > * {
  position: relative;
  z-index: 2;
}

.farm-bg-weather {
  background-image: url('https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: relative;
}

.farm-bg-weather::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.3);
  z-index: 1;
}

.farm-bg-weather > * {
  position: relative;
  z-index: 2;
}

.farm-bg-inventory {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%);
  background-size: cover;
  background-position: center;
  position: relative;
}

.farm-bg-inventory::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  z-index: 1;
}

.farm-bg-inventory > * {
  position: relative;
  z-index: 2;
}

.farm-bg-activities {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 50%, #0e7490 100%);
  background-size: cover;
  background-position: center;
  position: relative;
}

.farm-bg-activities::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  z-index: 1;
}

.farm-bg-activities > * {
  position: relative;
  z-index: 2;
}

.farm-bg-reports {
  background: linear-gradient(135deg, #ec4899 0%, #db2777 50%, #be185d 100%);
  background-size: cover;
  background-position: center;
  position: relative;
}

.farm-bg-reports::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  z-index: 1;
}

.farm-bg-reports > * {
  position: relative;
  z-index: 2;
}

.farm-bg-security {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
  background-size: cover;
  background-position: center;
  position: relative;
}

.farm-bg-security::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  z-index: 1;
}

.farm-bg-security > * {
  position: relative;
  z-index: 2;
}

/* Test background to ensure CSS is working */
.test-bg {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  min-height: 100vh;
}

/* Animated gradient for login page */
@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Page-specific backgrounds */
.page-login {
  background: #e0f2fe !important;
  min-height: 100vh !important;
}

.page-dashboard {
  background: #f0fdf4 !important;
  min-height: 100vh !important;
}

.page-farms {
  background: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%) !important;
  min-height: 100vh !important;
}

.page-finance {
  background: linear-gradient(135deg, #fefefe 0%, #f9fafb 100%) !important;
  min-height: 100vh !important;
}

.page-security {
  background: linear-gradient(135deg, #fefefe 0%, #f9fafb 100%) !important;
  min-height: 100vh !important;
}

@layer base {
  * {
    @apply border-gray-200;
  }

  body {
    @apply bg-gray-50 text-gray-900;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-white;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 h-10 py-2 px-4;
  }

  .btn-secondary {
    @apply btn bg-secondary-100 text-secondary-900 hover:bg-secondary-200 h-10 py-2 px-4;
  }

  .btn-outline {
    @apply btn border border-gray-300 hover:bg-gray-50 hover:text-gray-900 h-10 py-2 px-4;
  }

  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .card {
    @apply rounded-lg border border-gray-200 bg-white text-gray-900 shadow-sm;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-content {
    @apply p-6 pt-0;
  }
}
