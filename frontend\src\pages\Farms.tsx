import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Plus, MapPin, Edit, Trash2, MoreHorizontal, Loader2, Map, Eye } from 'lucide-react'
import { Link } from 'react-router-dom'
import { farmService, Farm, CreateFarmData, UpdateFarmData } from '../services/farmService'
import FarmModal from '../components/FarmModal'
import FarmMap from '../components/FarmMap'

export default function Farms() {
  const [searchTerm, setSearchTerm] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingFarm, setEditingFarm] = useState<Farm | null>(null)
  const [viewMode, setViewMode] = useState<'grid' | 'map'>('grid')
  const [selectedFarmOnMap, setSelectedFarmOnMap] = useState<number | null>(null)
  const queryClient = useQueryClient()

  // Fetch farms
  const { data: farms = [], isLoading, error } = useQuery({
    queryKey: ['farms'],
    queryFn: farmService.getFarms,
  })

  // Create farm mutation
  const createFarmMutation = useMutation({
    mutationFn: farmService.createFarm,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['farms'] })
      setIsModalOpen(false)
    },
  })

  // Update farm mutation
  const updateFarmMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateFarmData }) =>
      farmService.updateFarm(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['farms'] })
      setIsModalOpen(false)
      setEditingFarm(null)
    },
  })

  // Delete farm mutation
  const deleteFarmMutation = useMutation({
    mutationFn: farmService.deleteFarm,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['farms'] })
    },
  })

  const filteredFarms = farms.filter(farm =>
    farm.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    farm.location.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleAddFarm = () => {
    setEditingFarm(null)
    setIsModalOpen(true)
  }

  const handleEditFarm = (farm: Farm) => {
    setEditingFarm(farm)
    setIsModalOpen(true)
  }

  const handleDeleteFarm = async (farmId: number) => {
    if (window.confirm('Are you sure you want to delete this farm?')) {
      deleteFarmMutation.mutate(farmId)
    }
  }

  const handleModalSubmit = async (data: CreateFarmData | UpdateFarmData) => {
    if (editingFarm) {
      updateFarmMutation.mutate({ id: editingFarm.id, data })
    } else {
      createFarmMutation.mutate(data as CreateFarmData)
    }
  }

  return (
    <div className="farm-bg-farms min-h-screen -m-6 p-6">
      <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Farms</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your farm properties and locations
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {/* View Toggle */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                viewMode === 'grid'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Grid
            </button>
            <button
              onClick={() => setViewMode('map')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                viewMode === 'map'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Map className="h-4 w-4 mr-1" />
              Map
            </button>
          </div>

          <button className="btn-primary" onClick={handleAddFarm}>
            <Plus className="h-4 w-4 mr-2" />
            Add Farm
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <input
            type="text"
            placeholder="Search farms..."
            className="input"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <select className="input">
            <option>All Status</option>
            <option>Active</option>
            <option>Inactive</option>
          </select>
          <select className="input">
            <option>All Locations</option>
            <option>California</option>
            <option>Texas</option>
            <option>Colorado</option>
          </select>
        </div>
      </div>

      {/* Map View */}
      {viewMode === 'map' && (
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Farm Locations</h3>
            <p className="text-sm text-gray-500">Interactive map showing all farm locations and field boundaries</p>
          </div>
          <div className="card-content p-0">
            <FarmMap
              farms={filteredFarms.map(farm => ({
                id: farm.id,
                name: farm.name,
                coordinates: farm.coordinates,
                size: farm.size,
                sizeUnit: farm.sizeUnit,
                farmType: farm.farmType,
                fields: [
                  {
                    id: 1,
                    name: `${farm.name} Field A`,
                    coordinates: {
                      latitude: farm.coordinates.latitude + 0.001,
                      longitude: farm.coordinates.longitude + 0.001
                    },
                    size: `${Math.floor(farm.size / 3)} ${farm.sizeUnit}`,
                    cropType: farm.crops[0] || 'Mixed',
                    status: 'Active'
                  },
                  {
                    id: 2,
                    name: `${farm.name} Field B`,
                    coordinates: {
                      latitude: farm.coordinates.latitude - 0.001,
                      longitude: farm.coordinates.longitude + 0.002
                    },
                    size: `${Math.floor(farm.size / 4)} ${farm.sizeUnit}`,
                    cropType: farm.crops[1] || 'Fallow',
                    status: farm.crops[1] ? 'Active' : 'Fallow'
                  }
                ]
              }))}
              selectedFarm={selectedFarmOnMap}
              onFarmSelect={setSelectedFarmOnMap}
              height="500px"
            />
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary-600" />
          <span className="ml-2 text-gray-600">Loading farms...</span>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-600">Error loading farms. Please try again.</p>
        </div>
      )}

      {/* Farms Grid */}
      {viewMode === 'grid' && !isLoading && !error && (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {filteredFarms.map((farm) => (
          <div key={farm.id} className="card">
            <div className="card-content p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <MapPin className="h-6 w-6 text-primary-600" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-lg font-medium text-gray-900">{farm.name}</h3>
                    <p className="text-sm text-gray-500">{farm.location}</p>
                  </div>
                </div>
                <div className="relative">
                  <button className="p-1 rounded-full hover:bg-gray-100">
                    <MoreHorizontal className="h-4 w-4 text-gray-400" />
                  </button>
                </div>
              </div>

              <div className="mt-4 space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Size:</span>
                  <span className="font-medium text-gray-900">{farm.size}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Fields:</span>
                  <span className="font-medium text-gray-900">{farm.fields}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Status:</span>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    farm.status === 'Active' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {farm.status}
                  </span>
                </div>
              </div>

              <div className="mt-4">
                <p className="text-sm text-gray-500 mb-2">Crops:</p>
                <div className="flex flex-wrap gap-1">
                  {farm.crops.map((crop, index) => (
                    <span
                      key={index}
                      className="inline-flex px-2 py-1 text-xs font-medium bg-primary-100 text-primary-800 rounded-md"
                    >
                      {crop}
                    </span>
                  ))}
                </div>
              </div>

              <div className="mt-6 flex space-x-2">
                <Link
                  to={`/farms/${farm.id}`}
                  className="btn-primary flex-1 text-center"
                >
                  <Eye className="h-4 w-4 mr-1 inline" />
                  View Details
                </Link>
                <button
                  className="btn-outline"
                  onClick={() => handleEditFarm(farm)}
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  className="btn-outline text-red-600 hover:text-red-700 hover:bg-red-50"
                  onClick={() => handleDeleteFarm(farm.id)}
                  disabled={deleteFarmMutation.isPending}
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
        </div>
      )}

      {/* Empty State */}
      {filteredFarms.length === 0 && (
        <div className="text-center py-12">
          <MapPin className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No farms found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm ? 'Try adjusting your search criteria.' : 'Get started by creating your first farm.'}
          </p>
          {!searchTerm && (
            <div className="mt-6">
              <button className="btn-primary" onClick={handleAddFarm}>
                <Plus className="h-4 w-4 mr-2" />
                Add Farm
              </button>
            </div>
          )}
        </div>
      )}

      {/* Farm Modal */}
      <FarmModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false)
          setEditingFarm(null)
        }}
        onSubmit={handleModalSubmit}
        farm={editingFarm}
        loading={createFarmMutation.isPending || updateFarmMutation.isPending}
      />
      </div>
    </div>
  )
}
