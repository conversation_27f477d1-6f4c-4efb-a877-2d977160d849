import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { <PERSON>p<PERSON>, Mail, Lock, Eye, EyeOff, User, Shield, AlertTriangle } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import { securityService } from '../services/securityService'

interface LoginForm {
  name: string
  email: string
  password: string
}

export default function Login() {
  const [showPassword, setShowPassword] = useState(false)
  const [securityWarning, setSecurityWarning] = useState<string | null>(null)
  const { login, loading } = useAuth()
  const { register, handleSubmit, formState: { errors }, setError, watch } = useForm<LoginForm>()

  const watchedEmail = watch('email')
  const watchedPassword = watch('password')

  const onSubmit = async (data: LoginForm) => {
    try {
      setSecurityWarning(null)

      // Check if account is locked
      if (securityService.isAccountLocked(data.email)) {
        setSecurityWarning('Account is temporarily locked due to multiple failed login attempts. Please try again later.')
        return
      }

      await login(data.email, data.password, data.name)
    } catch (error: any) {
      setError('root', { message: error.message || 'Invalid credentials' })

      // Show additional security warnings
      if (error.message?.includes('locked')) {
        setSecurityWarning('Your account has been temporarily locked for security reasons.')
      } else if (error.message?.includes('rate limit')) {
        setSecurityWarning('Too many login attempts. Please wait before trying again.')
      }
    }
  }

  // Check password strength in real-time
  const getPasswordStrength = (password: string) => {
    if (!password) return null
    const validation = securityService.validatePassword(password)
    return validation
  }

  return (
    <div className="farm-bg-login min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="flex justify-center">
            <div className="flex items-center">
              <Sprout className="h-12 w-12 text-primary-600" />
              <span className="ml-2 text-3xl font-bold text-gray-900">FarmOS</span>
            </div>
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Welcome to Farm App
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Sign in to manage your farm operations efficiently
          </p>
          <div className="mt-4 text-center">
            <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
              🌾 Your Smart Farm Management Solution
            </div>
          </div>
        </div>
        
        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Full Name
              </label>
              <div className="mt-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <User className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  {...register('name', {
                    required: 'Name is required',
                    minLength: {
                      value: 2,
                      message: 'Name must be at least 2 characters'
                    },
                    pattern: {
                      value: /^[a-zA-Z\s]+$/,
                      message: 'Name should only contain letters and spaces'
                    }
                  })}
                  type="text"
                  className="input pl-10"
                  placeholder="e.g., John Smith, Sarah Johnson"
                  style={{ textTransform: 'capitalize' }}
                />
              </div>
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <div className="mt-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  {...register('email', { 
                    required: 'Email is required',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Invalid email address'
                    }
                  })}
                  type="email"
                  className="input pl-10"
                  placeholder="Enter your email"
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div className="mt-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  {...register('password', { 
                    required: 'Password is required',
                    minLength: {
                      value: 6,
                      message: 'Password must be at least 6 characters'
                    }
                  })}
                  type={showPassword ? 'text' : 'password'}
                  className="input pl-10 pr-10"
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
              )}

              {/* Password Strength Indicator */}
              {watchedPassword && (
                <div className="mt-2">
                  {(() => {
                    const strength = getPasswordStrength(watchedPassword)
                    if (!strength?.isValid && strength?.errors.length > 0) {
                      return (
                        <div className="text-xs text-orange-600">
                          <p className="font-medium">Password requirements:</p>
                          <ul className="list-disc list-inside mt-1">
                            {strength.errors.map((error, index) => (
                              <li key={index}>{error}</li>
                            ))}
                          </ul>
                        </div>
                      )
                    } else if (strength?.isValid) {
                      return (
                        <div className="flex items-center text-xs text-green-600">
                          <Shield className="h-3 w-3 mr-1" />
                          Strong password
                        </div>
                      )
                    }
                    return null
                  })()}
                </div>
              )}
            </div>
          </div>

          {/* Security Warning */}
          {securityWarning && (
            <div className="bg-orange-50 border border-orange-200 rounded-md p-3">
              <div className="flex items-start">
                <AlertTriangle className="h-4 w-4 text-orange-500 mt-0.5 mr-2" />
                <p className="text-sm text-orange-700">{securityWarning}</p>
              </div>
            </div>
          )}

          {errors.root && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-600">{errors.root.message}</p>
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={loading}
              className="btn-primary w-full"
            >
              {loading ? 'Signing in...' : 'Sign in'}
            </button>
          </div>

          <div className="text-center">
            <div className="bg-green-50 border border-green-200 rounded-md p-3 mb-4">
              <p className="text-sm text-green-800 font-medium">🌾 Farm App Login</p>
              <p className="text-xs text-green-600 mt-1">
                Enter your details to access your farm management dashboard
              </p>
              <div className="mt-2 text-xs text-green-600">
                <strong>Example:</strong> Name: "John Smith", Email: "<EMAIL>", Password: "farm123"
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 text-xs text-gray-500">
              <div>
                <p className="font-medium">✨ Features:</p>
                <ul className="mt-1 space-y-1 text-left">
                  <li>• Crop Management</li>
                  <li>• Financial Tracking</li>
                  <li>• Weather Integration</li>
                </ul>
              </div>
              <div>
                <p className="font-medium">📊 Analytics:</p>
                <ul className="mt-1 space-y-1 text-left">
                  <li>• Performance Charts</li>
                  <li>• Detailed Reports</li>
                  <li>• Real-time Data</li>
                </ul>
              </div>
              <div>
                <p className="font-medium">🔒 Security:</p>
                <ul className="mt-1 space-y-1 text-left">
                  <li>• Data Encryption</li>
                  <li>• Activity Monitoring</li>
                  <li>• Account Protection</li>
                </ul>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}
