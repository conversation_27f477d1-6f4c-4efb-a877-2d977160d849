import { useState, useEffect } from 'react'
import { <PERSON><PERSON><PERSON>, Layers, Sprout, Calendar, TrendingUp, AlertTriangle, Droplets, Thermometer, Sun, Moon } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js'
import { Line } from 'react-chartjs-2'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
)

export default function Dashboard() {
  const { user } = useAuth()
  const [farms, setFarms] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch farms data
  useEffect(() => {
    const fetchFarms = async () => {
      try {
        const response = await fetch('http://localhost:3001/api/farms')
        const data = await response.json()

        if (data.success) {
          setFarms(data.data)
        } else {
          setError('Failed to fetch farms')
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error'
        setError(`Error connecting to server: ${errorMessage}`)
        console.error('Error fetching farms:', err)
        console.error('API URL:', 'http://localhost:3001/api/farms')
      } finally {
        setLoading(false)
      }
    }

    fetchFarms()
  }, [])

  // Calculate dynamic stats based on real farm data
  const stats = [
    { name: 'Total Farms', value: farms.length.toString(), icon: MapPin, change: '+1', changeType: 'positive' },
    { name: 'Active Fields', value: farms.reduce((total, farm) => total + (farm.fields || 0), 0).toString(), icon: Layers, change: '+3', changeType: 'positive' },
    { name: 'Crops Planted', value: farms.reduce((total, farm) => total + (farm.crops?.length || 0), 0).toString(), icon: Sprout, change: '+2', changeType: 'positive' },
    { name: 'Pending Tasks', value: '12', icon: Calendar, change: '-2', changeType: 'negative' },
  ]

  // Transform farm data for display
  const farmSummaries = farms.map(farm => ({
    id: farm._id,
    name: farm.name,
    location: farm.location,
    size: `${farm.size} ${farm.sizeUnit}`,
    fields: farm.fields,
    activeCrops: farm.crops?.length || 0,
    status: farm.status,
    revenue: new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XAF',
      minimumFractionDigits: 0,
    }).format(farm.totalRevenue || 0),
    lastActivity: '2 hours ago' // This would come from activities in a real app
  }))

const recentActivities = [
  { id: 1, activity: 'Maize planting completed', field: 'Ferme Bamenda - Field A', time: '2 hours ago', status: 'completed' },
  { id: 2, activity: 'Irrigation scheduled', field: 'Plantation Kribi - Field B', time: '4 hours ago', status: 'scheduled' },
  { id: 3, activity: 'Fertilizer application', field: 'Ferme Maroua - Field C', time: '1 day ago', status: 'in-progress' },
  { id: 4, activity: 'Cocoa harvesting', field: 'Plantation Bertoua - Field D', time: '2 days ago', status: 'completed' },
]

const alerts = [
  { id: 1, message: 'Low soil moisture detected in Field A', type: 'warning', time: '1 hour ago' },
  { id: 2, message: 'Weather alert: Heavy rain expected tomorrow', type: 'info', time: '3 hours ago' },
  { id: 3, message: 'Equipment maintenance due for Tractor #3', type: 'warning', time: '1 day ago' },
]

  // Get current time for greeting
  const getCurrentGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return {
      greeting: 'Good Morning',
      icon: Sun,
      message: 'Ready to start another productive day on the farm!'
    }
    if (hour < 17) return {
      greeting: 'Good Afternoon',
      icon: Sun,
      message: 'Hope your day is going well! Keep up the great work.'
    }
    return {
      greeting: 'Good Evening',
      icon: Moon,
      message: 'Time to review today\'s progress and plan for tomorrow.'
    }
  }

  const { greeting, icon: GreetingIcon, message } = getCurrentGreeting()

  // Get motivational message based on performance
  const getMotivationalMessage = () => {
    const messages = [
      "Your farm efficiency is above average! 🌟",
      "Great job managing your crops this season! 🌾",
      "Your financial performance is trending upward! 📈",
      "Keep up the excellent work with your farm operations! 💪"
    ]
    return messages[Math.floor(Math.random() * messages.length)]
  }

  // Quick performance chart data
  const performanceData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Farm Performance',
        data: [85, 88, 92, 89, 94, 91],
        borderColor: 'rgba(34, 197, 94, 1)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        borderWidth: 3,
        fill: true,
        tension: 0.4,
      },
    ],
  }

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
    },
    scales: {
      x: {
        display: false,
      },
      y: {
        display: false,
        beginAtZero: true,
      }
    },
    elements: {
      point: {
        radius: 0,
      },
    },
  }

  // Loading state
  if (loading) {
    return (
      <div className="farm-bg-dashboard min-h-screen -m-6 p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading farm data...</p>
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="farm-bg-dashboard min-h-screen -m-6 p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Data</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="btn-primary"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="farm-bg-dashboard min-h-screen -m-6 p-6">
      <div className="space-y-6">
      {/* Welcome Message */}
      <div className="bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg shadow-lg">
        <div className="px-6 py-8 sm:px-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex-shrink-0">
                <GreetingIcon className="h-12 w-12 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">
                  {greeting}, {user?.name?.split(' ')[0] || 'Farmer'}! 👋
                </h1>
                <p className="mt-1 text-primary-100">
                  {message}
                </p>
                <p className="mt-2 text-sm text-primary-200">
                  {getMotivationalMessage()}
                </p>
              </div>
            </div>
            <div className="hidden sm:block">
              <div className="text-right text-white">
                <div className="text-sm opacity-90">
                  {new Date().toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </div>
                <div className="text-xs opacity-75 mt-1">
                  {new Date().toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </div>
              </div>
            </div>
          </div>

          {/* Quick Stats in Welcome Banner */}
          <div className="mt-6 grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div className="bg-white bg-opacity-20 rounded-lg p-4">
              <div className="flex items-center">
                <Sprout className="h-6 w-6 text-white" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-primary-100">Active Crops</p>
                  <p className="text-lg font-semibold text-white">156</p>
                </div>
              </div>
            </div>
            <div className="bg-white bg-opacity-20 rounded-lg p-4">
              <div className="flex items-center">
                <Calendar className="h-6 w-6 text-white" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-primary-100">Today's Tasks</p>
                  <p className="text-lg font-semibold text-white">8</p>
                </div>
              </div>
            </div>
            <div className="bg-white bg-opacity-20 rounded-lg p-4">
              <div className="flex items-center">
                <TrendingUp className="h-6 w-6 text-white" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-primary-100">This Month</p>
                  <p className="text-lg font-semibold text-white">+12.5%</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Dashboard Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Farm Overview</h2>
          <p className="mt-1 text-sm text-gray-500">
            Monitor your farm operations and key performance metrics.
          </p>
        </div>
        <div className="hidden sm:block">
          <button
            onClick={() => window.location.href = '/?logout=true'}
            className="text-xs text-gray-500 hover:text-gray-700 px-3 py-1 rounded-md border border-gray-300 hover:bg-gray-50"
          >
            🔄 New Session
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((item) => (
          <div key={item.name} className="card card-overlay">
            <div className="card-content p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <item.icon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">{item.name}</dt>
                    <dd className="flex items-baseline">
                      <div className="text-2xl font-semibold text-gray-900">{item.value}</div>
                      <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                        item.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {item.change}
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Weather Widget */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Current Weather</h3>
          </div>
          <div className="card-content">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                    <Droplets className="h-8 w-8 text-blue-600" />
                  </div>
                </div>
                <div>
                  <p className="text-2xl font-semibold text-gray-900">22°C</p>
                  <p className="text-sm text-gray-500">Partly Cloudy</p>
                </div>
              </div>
              <div className="text-right">
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Thermometer className="h-4 w-4" />
                  <span>Humidity: 65%</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-500 mt-1">
                  <Droplets className="h-4 w-4" />
                  <span>Rain: 20%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Performance Overview */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Performance Overview</h3>
          </div>
          <div className="card-content">
            <div className="h-32 mb-4">
              <Line data={performanceData} options={chartOptions} />
            </div>
            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-green-600">91%</div>
                <div className="text-sm text-gray-500">Current Efficiency</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600">+6.2%</div>
                <div className="text-sm text-gray-500">Monthly Growth</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Quick Actions */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
            <p className="text-sm text-gray-600">Access farm management features</p>
          </div>
          <div className="card-content">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <button
                onClick={() => window.location.href = '/farms'}
                className="btn-outline text-left p-3 h-auto"
              >
                <MapPin className="h-5 w-5 mb-2" />
                <div className="text-sm font-medium">View All Farms</div>
                <div className="text-xs text-gray-500">Manage farm operations</div>
              </button>
              <button
                onClick={() => {
                  window.location.href = '/farms'
                  setTimeout(() => {
                    alert('Select a farm to add crops, schedule activities, and manage operations')
                  }, 100)
                }}
                className="btn-outline text-left p-3 h-auto"
              >
                <Sprout className="h-5 w-5 mb-2" />
                <div className="text-sm font-medium">Manage Crops</div>
                <div className="text-xs text-gray-500">Add and track crops</div>
              </button>
              <button
                onClick={() => {
                  window.location.href = '/farms'
                  setTimeout(() => {
                    alert('Select a farm to schedule activities and track progress')
                  }, 100)
                }}
                className="btn-outline text-left p-3 h-auto"
              >
                <Calendar className="h-5 w-5 mb-2" />
                <div className="text-sm font-medium">Schedule Activities</div>
                <div className="text-xs text-gray-500">Plan farm activities</div>
              </button>
              <button
                onClick={() => {
                  window.location.href = '/farms'
                  setTimeout(() => {
                    alert('Select a farm to view detailed reports and analytics')
                  }, 100)
                }}
                className="btn-outline text-left p-3 h-auto"
              >
                <TrendingUp className="h-5 w-5 mb-2" />
                <div className="text-sm font-medium">View Reports</div>
                <div className="text-xs text-gray-500">Analytics and insights</div>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Farm Summaries */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">Farm Overview</h3>
          <p className="text-sm text-gray-600">Summary of all farms across Cameroon</p>
        </div>
        <div className="card-content">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
            {farmSummaries.map((farm) => (
              <div key={farm.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-semibold text-gray-900 text-sm">{farm.name}</h4>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    farm.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {farm.status}
                  </span>
                </div>
                <div className="space-y-2 text-xs text-gray-600">
                  <div className="flex items-center">
                    <MapPin className="h-3 w-3 mr-1" />
                    {farm.location}
                  </div>
                  <div className="flex justify-between">
                    <span>Size:</span>
                    <span className="font-medium">{farm.size}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Fields:</span>
                    <span className="font-medium">{farm.fields}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Active Crops:</span>
                    <span className="font-medium">{farm.activeCrops}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Monthly Revenue:</span>
                    <span className="font-medium text-green-600">{farm.revenue}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Last Activity:</span>
                    <span className="font-medium">{farm.lastActivity}</span>
                  </div>
                </div>
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <button
                    onClick={() => window.location.href = `/farms/${farm.id}`}
                    className="btn-primary text-xs w-full"
                  >
                    View Details
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Recent Activities */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Recent Activities</h3>
          </div>
          <div className="card-content">
            <div className="flow-root">
              <ul className="-mb-8">
                {recentActivities.map((activity, activityIdx) => (
                  <li key={activity.id}>
                    <div className="relative pb-8">
                      {activityIdx !== recentActivities.length - 1 ? (
                        <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" />
                      ) : null}
                      <div className="relative flex space-x-3">
                        <div>
                          <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${
                            activity.status === 'completed' ? 'bg-green-500' :
                            activity.status === 'in-progress' ? 'bg-yellow-500' : 'bg-blue-500'
                          }`}>
                            <Calendar className="h-4 w-4 text-white" />
                          </span>
                        </div>
                        <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                          <div>
                            <p className="text-sm text-gray-900">{activity.activity}</p>
                            <p className="text-sm text-gray-500">{activity.field}</p>
                          </div>
                          <div className="text-right text-sm whitespace-nowrap text-gray-500">
                            {activity.time}
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Alerts */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Alerts & Notifications</h3>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              {alerts.map((alert) => (
                <div key={alert.id} className={`p-3 rounded-md ${
                  alert.type === 'warning' ? 'bg-yellow-50 border border-yellow-200' : 'bg-blue-50 border border-blue-200'
                }`}>
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <AlertTriangle className={`h-5 w-5 ${
                        alert.type === 'warning' ? 'text-yellow-400' : 'text-blue-400'
                      }`} />
                    </div>
                    <div className="ml-3 flex-1">
                      <p className={`text-sm font-medium ${
                        alert.type === 'warning' ? 'text-yellow-800' : 'text-blue-800'
                      }`}>
                        {alert.message}
                      </p>
                      <p className={`text-xs mt-1 ${
                        alert.type === 'warning' ? 'text-yellow-600' : 'text-blue-600'
                      }`}>
                        {alert.time}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      </div>
    </div>
  )
}
