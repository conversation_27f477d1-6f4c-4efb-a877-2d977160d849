import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom'
import { 
  ArrowLeft, 
  MapPin, 
  Calendar, 
  Sprout, 
  Layers, 
  Package, 
  DollarSign, 
  BarChart3,
  Activity,
  Users,
  TrendingUp,
  AlertCircle
} from 'lucide-react'

interface Farm {
  id: string
  name: string
  location: string
  size: number
  type: string
  status: 'Active' | 'Inactive'
  owner: string
  contact: string
  description: string
}

interface FarmStats {
  totalFields: number
  activeCrops: number
  totalActivities: number
  monthlyRevenue: number
  monthlyExpenses: number
  inventoryItems: number
  lastActivity: string
}

export default function FarmDetail() {
  const { farmId } = useParams<{ farmId: string }>()
  const [farm, setFarm] = useState<Farm | null>(null)
  const [stats, setStats] = useState<FarmStats | null>(null)
  const [activeTab, setActiveTab] = useState<'overview' | 'fields' | 'crops' | 'activities' | 'inventory' | 'finance' | 'reports'>('overview')

  useEffect(() => {
    // Load farm data (in real app, this would be from API)
    const mockFarm: Farm = {
      id: farmId || '1',
      name: 'Green Valley Farm',
      location: 'Douala, Cameroon',
      size: 150,
      type: 'Mixed Farming',
      status: 'Active',
      owner: 'Jean Baptiste',
      contact: '+237 6XX XXX XXX',
      description: 'A productive mixed farming operation specializing in maize, cassava, and livestock.'
    }

    const mockStats: FarmStats = {
      totalFields: 8,
      activeCrops: 5,
      totalActivities: 24,
      monthlyRevenue: 2500000, // CFA
      monthlyExpenses: 1800000, // CFA
      inventoryItems: 45,
      lastActivity: '2 hours ago'
    }

    setFarm(mockFarm)
    setStats(mockStats)
  }, [farmId])

  if (!farm || !stats) {
    return (
      <div className="farm-bg-farms min-h-screen -m-6 p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading farm details...</p>
          </div>
        </div>
      </div>
    )
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XAF',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'fields', label: 'Fields', icon: Layers },
    { id: 'crops', label: 'Crops', icon: Sprout },
    { id: 'activities', label: 'Activities', icon: Calendar },
    { id: 'inventory', label: 'Inventory', icon: Package },
    { id: 'finance', label: 'Finance', icon: DollarSign },
    { id: 'reports', label: 'Reports', icon: BarChart3 }
  ]

  return (
    <div className="farm-bg-farms min-h-screen -m-6 p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link 
              to="/farms" 
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Farms
            </Link>
          </div>
        </div>

        {/* Farm Header */}
        <div className="bg-white rounded-lg shadow-lg p-6 card-overlay">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-3">
                <h1 className="text-3xl font-bold text-gray-900">{farm.name}</h1>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  farm.status === 'Active' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {farm.status}
                </span>
              </div>
              <div className="mt-2 space-y-2">
                <div className="flex items-center text-gray-600">
                  <MapPin className="h-4 w-4 mr-2" />
                  {farm.location}
                </div>
                <div className="flex items-center space-x-6 text-sm text-gray-600">
                  <span><strong>Size:</strong> {farm.size} hectares</span>
                  <span><strong>Type:</strong> {farm.type}</span>
                  <span><strong>Owner:</strong> {farm.owner}</span>
                </div>
              </div>
              <p className="mt-3 text-gray-700">{farm.description}</p>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-500">Last Activity</div>
              <div className="text-lg font-semibold text-gray-900">{stats.lastActivity}</div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6 card-overlay">
            <div className="flex items-center">
              <Layers className="h-8 w-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Fields</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalFields}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6 card-overlay">
            <div className="flex items-center">
              <Sprout className="h-8 w-8 text-green-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Crops</p>
                <p className="text-2xl font-bold text-gray-900">{stats.activeCrops}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6 card-overlay">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-emerald-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Monthly Revenue</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.monthlyRevenue)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6 card-overlay">
            <div className="flex items-center">
              <Package className="h-8 w-8 text-purple-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Inventory Items</p>
                <p className="text-2xl font-bold text-gray-900">{stats.inventoryItems}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white rounded-lg shadow card-overlay">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-green-500 text-green-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="h-4 w-4 mr-2" />
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'overview' && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Financial Overview */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Financial Overview</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Monthly Revenue:</span>
                        <span className="font-semibold text-green-600">{formatCurrency(stats.monthlyRevenue)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Monthly Expenses:</span>
                        <span className="font-semibold text-red-600">{formatCurrency(stats.monthlyExpenses)}</span>
                      </div>
                      <div className="flex justify-between border-t pt-2">
                        <span className="text-gray-900 font-semibold">Net Profit:</span>
                        <span className="font-bold text-green-600">
                          {formatCurrency(stats.monthlyRevenue - stats.monthlyExpenses)}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Recent Activities */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activities</h3>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <Activity className="h-4 w-4 text-blue-500" />
                        <span className="text-sm text-gray-700">Irrigation - Field A</span>
                        <span className="text-xs text-gray-500">2 hours ago</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Sprout className="h-4 w-4 text-green-500" />
                        <span className="text-sm text-gray-700">Planting - Maize</span>
                        <span className="text-xs text-gray-500">1 day ago</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Package className="h-4 w-4 text-purple-500" />
                        <span className="text-sm text-gray-700">Fertilizer Application</span>
                        <span className="text-xs text-gray-500">3 days ago</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'fields' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Farm Fields</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[1, 2, 3, 4, 5, 6, 7, 8].map((field) => (
                    <div key={field} className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900">Field {field}</h4>
                      <div className="mt-2 space-y-1 text-sm text-gray-600">
                        <div>Size: {Math.floor(Math.random() * 20) + 5} hectares</div>
                        <div>Crop: {['Maize', 'Cassava', 'Plantain', 'Cocoa'][Math.floor(Math.random() * 4)]}</div>
                        <div>Status: {['Growing', 'Planted', 'Ready for Harvest'][Math.floor(Math.random() * 3)]}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'crops' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Active Crops</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {['Maize', 'Cassava', 'Plantain', 'Cocoa', 'Tomatoes'].map((crop, index) => (
                    <div key={crop} className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900">{crop}</h4>
                      <div className="mt-2 space-y-1 text-sm text-gray-600">
                        <div>Planted: {Math.floor(Math.random() * 30) + 1} days ago</div>
                        <div>Expected Harvest: {Math.floor(Math.random() * 60) + 30} days</div>
                        <div>Health: {['Excellent', 'Good', 'Fair'][Math.floor(Math.random() * 3)]}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'activities' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Farm Activities</h3>
                <div className="space-y-4">
                  {[
                    { activity: 'Irrigation', field: 'Field A', time: '2 hours ago', status: 'Completed' },
                    { activity: 'Fertilizer Application', field: 'Field B', time: '1 day ago', status: 'Completed' },
                    { activity: 'Pest Control', field: 'Field C', time: '2 days ago', status: 'In Progress' },
                    { activity: 'Harvesting', field: 'Field D', time: '3 days ago', status: 'Scheduled' }
                  ].map((activity, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4 flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold text-gray-900">{activity.activity}</h4>
                        <p className="text-sm text-gray-600">{activity.field} • {activity.time}</p>
                      </div>
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                        activity.status === 'Completed' ? 'bg-green-100 text-green-800' :
                        activity.status === 'In Progress' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {activity.status}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'inventory' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Farm Inventory</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[
                    { item: 'Seeds - Maize', quantity: '50 kg', status: 'In Stock' },
                    { item: 'Fertilizer - NPK', quantity: '200 kg', status: 'Low Stock' },
                    { item: 'Pesticide', quantity: '15 liters', status: 'In Stock' },
                    { item: 'Tools - Hoes', quantity: '12 units', status: 'In Stock' },
                    { item: 'Irrigation Pipes', quantity: '500 meters', status: 'In Stock' },
                    { item: 'Seeds - Cassava', quantity: '5 kg', status: 'Out of Stock' }
                  ].map((item, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900">{item.item}</h4>
                      <div className="mt-2 flex items-center justify-between">
                        <span className="text-sm text-gray-600">{item.quantity}</span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.status === 'In Stock' ? 'bg-green-100 text-green-800' :
                          item.status === 'Low Stock' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {item.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'finance' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Financial Details</h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3">Revenue Sources</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Crop Sales:</span>
                        <span className="font-semibold">{formatCurrency(2000000)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Livestock:</span>
                        <span className="font-semibold">{formatCurrency(300000)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Other:</span>
                        <span className="font-semibold">{formatCurrency(200000)}</span>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3">Expenses</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Seeds & Fertilizer:</span>
                        <span className="font-semibold">{formatCurrency(800000)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Labor:</span>
                        <span className="font-semibold">{formatCurrency(600000)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Equipment:</span>
                        <span className="font-semibold">{formatCurrency(400000)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'reports' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Farm Reports</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {[
                    { title: 'Monthly Production Report', date: 'December 2024', status: 'Ready' },
                    { title: 'Financial Summary', date: 'December 2024', status: 'Ready' },
                    { title: 'Crop Health Assessment', date: 'December 2024', status: 'In Progress' },
                    { title: 'Inventory Status Report', date: 'December 2024', status: 'Ready' }
                  ].map((report, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4 flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold text-gray-900">{report.title}</h4>
                        <p className="text-sm text-gray-600">{report.date}</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                          report.status === 'Ready' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {report.status}
                        </span>
                        {report.status === 'Ready' && (
                          <button className="btn-primary text-sm">Download</button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'fields' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Farm Fields</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[1, 2, 3, 4, 5, 6, 7, 8].map((field) => (
                    <div key={field} className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900">Field {field}</h4>
                      <div className="mt-2 space-y-1 text-sm text-gray-600">
                        <div>Size: {Math.floor(Math.random() * 20) + 5} hectares</div>
                        <div>Crop: {['Maize', 'Cassava', 'Plantain', 'Cocoa'][Math.floor(Math.random() * 4)]}</div>
                        <div>Status: {['Growing', 'Planted', 'Ready for Harvest'][Math.floor(Math.random() * 3)]}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'crops' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Active Crops</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {['Maize', 'Cassava', 'Plantain', 'Cocoa', 'Tomatoes'].map((crop, index) => (
                    <div key={crop} className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900">{crop}</h4>
                      <div className="mt-2 space-y-1 text-sm text-gray-600">
                        <div>Planted: {Math.floor(Math.random() * 30) + 1} days ago</div>
                        <div>Expected Harvest: {Math.floor(Math.random() * 60) + 30} days</div>
                        <div>Health: {['Excellent', 'Good', 'Fair'][Math.floor(Math.random() * 3)]}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'activities' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Farm Activities</h3>
                <div className="space-y-4">
                  {[
                    { activity: 'Irrigation', field: 'Field A', time: '2 hours ago', status: 'Completed' },
                    { activity: 'Fertilizer Application', field: 'Field B', time: '1 day ago', status: 'Completed' },
                    { activity: 'Pest Control', field: 'Field C', time: '2 days ago', status: 'In Progress' },
                    { activity: 'Harvesting', field: 'Field D', time: '3 days ago', status: 'Scheduled' }
                  ].map((activity, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4 flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold text-gray-900">{activity.activity}</h4>
                        <p className="text-sm text-gray-600">{activity.field} • {activity.time}</p>
                      </div>
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                        activity.status === 'Completed' ? 'bg-green-100 text-green-800' :
                        activity.status === 'In Progress' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {activity.status}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'inventory' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Farm Inventory</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[
                    { item: 'Seeds - Maize', quantity: '50 kg', status: 'In Stock' },
                    { item: 'Fertilizer - NPK', quantity: '200 kg', status: 'Low Stock' },
                    { item: 'Pesticide', quantity: '15 liters', status: 'In Stock' },
                    { item: 'Tools - Hoes', quantity: '12 units', status: 'In Stock' },
                    { item: 'Irrigation Pipes', quantity: '500 meters', status: 'In Stock' },
                    { item: 'Seeds - Cassava', quantity: '5 kg', status: 'Out of Stock' }
                  ].map((item, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900">{item.item}</h4>
                      <div className="mt-2 flex items-center justify-between">
                        <span className="text-sm text-gray-600">{item.quantity}</span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.status === 'In Stock' ? 'bg-green-100 text-green-800' :
                          item.status === 'Low Stock' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {item.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'finance' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Financial Details</h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3">Revenue Sources</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Crop Sales:</span>
                        <span className="font-semibold">{formatCurrency(2000000)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Livestock:</span>
                        <span className="font-semibold">{formatCurrency(300000)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Other:</span>
                        <span className="font-semibold">{formatCurrency(200000)}</span>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3">Expenses</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Seeds & Fertilizer:</span>
                        <span className="font-semibold">{formatCurrency(800000)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Labor:</span>
                        <span className="font-semibold">{formatCurrency(600000)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Equipment:</span>
                        <span className="font-semibold">{formatCurrency(400000)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'reports' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Farm Reports</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {[
                    { title: 'Monthly Production Report', date: 'December 2024', status: 'Ready' },
                    { title: 'Financial Summary', date: 'December 2024', status: 'Ready' },
                    { title: 'Crop Health Assessment', date: 'December 2024', status: 'In Progress' },
                    { title: 'Inventory Status Report', date: 'December 2024', status: 'Ready' }
                  ].map((report, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4 flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold text-gray-900">{report.title}</h4>
                        <p className="text-sm text-gray-600">{report.date}</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                          report.status === 'Ready' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {report.status}
                        </span>
                        {report.status === 'Ready' && (
                          <button className="btn-primary text-sm">Download</button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
