import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import {
  ArrowLeft,
  MapPin,
  Calendar,
  Sprout,
  Layers,
  Package,
  DollarSign,
  BarChart3,
  Activity,
  Users,
  TrendingUp,
  AlertCircle,
  Cloud,
  Thermometer,
  Droplets,
  Wind,
  Sun
} from 'lucide-react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js'
import { Bar, Line, Doughnut } from 'react-chartjs-2'
import FarmDetailMap from '../components/FarmDetailMap'

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
)

interface Farm {
  id: string
  name: string
  location: string
  size: number
  type: string
  status: 'Active' | 'Inactive'
  owner: string
  contact: string
  description: string
}

interface FarmStats {
  totalFields: number
  activeCrops: number
  totalActivities: number
  monthlyRevenue: number
  monthlyExpenses: number
  inventoryItems: number
  lastActivity: string
}

interface WeatherData {
  current: {
    temperature: number
    humidity: number
    windSpeed: number
    condition: string
    icon: string
  }
  forecast: Array<{
    day: string
    high: number
    low: number
    condition: string
    precipitation: number
  }>
}

export default function FarmDetail() {
  const { farmId } = useParams<{ farmId: string }>()
  const [farm, setFarm] = useState<Farm | null>(null)
  const [stats, setStats] = useState<FarmStats | null>(null)
  const [weather, setWeather] = useState<WeatherData | null>(null)
  const [activeTab, setActiveTab] = useState<'overview' | 'fields' | 'crops' | 'activities' | 'inventory' | 'finance' | 'weather' | 'map' | 'reports'>('overview')
  const [selectedReport, setSelectedReport] = useState<any>(null)
  const [isReportModalOpen, setIsReportModalOpen] = useState(false)
  const [isScheduleModalOpen, setIsScheduleModalOpen] = useState(false)
  const [isTransactionModalOpen, setIsTransactionModalOpen] = useState(false)
  const [isCropModalOpen, setIsCropModalOpen] = useState(false)
  const [isActivityModalOpen, setIsActivityModalOpen] = useState(false)
  const [selectedCrop, setSelectedCrop] = useState<any>(null)
  const [selectedActivity, setSelectedActivity] = useState<any>(null)

  useEffect(() => {
    // Load farm data (in real app, this would be from API)
    const cameroonFarms = [
      {
        id: '1',
        name: 'Ferme Agricole Bamenda',
        location: 'Bamenda, North West Region',
        size: 180,
        type: 'Mixed Farming',
        status: 'Active',
        owner: 'Ngozi Mbah',
        contact: '+237 677 123 456',
        description: 'Une exploitation agricole productive spécialisée dans le maïs, le manioc et l\'élevage dans les hautes terres de Bamenda.'
      },
      {
        id: '2',
        name: 'Plantation Kribi',
        location: 'Kribi, South Region',
        size: 250,
        type: 'Plantation',
        status: 'Active',
        owner: 'Paul Biya Essomba',
        contact: '+237 699 234 567',
        description: 'Grande plantation côtière spécialisée dans la culture du cacao, de l\'hévéa et des fruits tropicaux.'
      },
      {
        id: '3',
        name: 'Ferme Maroua',
        location: 'Maroua, Far North Region',
        size: 120,
        type: 'Crop Farming',
        status: 'Active',
        owner: 'Aminatou Alhadji',
        contact: '+237 655 345 678',
        description: 'Exploitation agricole sahélienne concentrée sur le mil, le sorgho et l\'élevage de bovins.'
      },
      {
        id: '4',
        name: 'Agro-Ferme Bafoussam',
        location: 'Bafoussam, West Region',
        size: 95,
        type: 'Vegetable Farming',
        status: 'Active',
        owner: 'Marie Tchounga',
        contact: '+237 681 456 789',
        description: 'Ferme moderne spécialisée dans les légumes, les pommes de terre et les cultures maraîchères des hauts plateaux.'
      },
      {
        id: '5',
        name: 'Plantation Bertoua',
        location: 'Bertoua, East Region',
        size: 300,
        type: 'Forest Farming',
        status: 'Active',
        owner: 'Jean-Claude Mvondo',
        contact: '+237 670 567 890',
        description: 'Exploitation forestière et agricole combinant la culture du cacao, du café et l\'exploitation durable du bois.'
      }
    ]

    const selectedFarm = cameroonFarms.find(f => f.id === farmId) || cameroonFarms[0]
    const mockFarm: Farm = {
      ...selectedFarm,
      status: selectedFarm.status as 'Active' | 'Inactive'
    }

    const mockStats: FarmStats = {
      totalFields: 8,
      activeCrops: 5,
      totalActivities: 24,
      monthlyRevenue: 2500000, // CFA
      monthlyExpenses: 1800000, // CFA
      inventoryItems: 45,
      lastActivity: '2 hours ago'
    }

    const mockWeather: WeatherData = {
      current: {
        temperature: 28,
        humidity: 75,
        windSpeed: 12,
        condition: 'Partly Cloudy',
        icon: '⛅'
      },
      forecast: [
        { day: 'Today', high: 30, low: 22, condition: 'Sunny', precipitation: 10 },
        { day: 'Tomorrow', high: 28, low: 20, condition: 'Cloudy', precipitation: 40 },
        { day: 'Wednesday', high: 26, low: 19, condition: 'Rainy', precipitation: 80 },
        { day: 'Thursday', high: 29, low: 21, condition: 'Sunny', precipitation: 5 },
        { day: 'Friday', high: 31, low: 23, condition: 'Partly Cloudy', precipitation: 20 }
      ]
    }

    setFarm(mockFarm)
    setStats(mockStats)
    setWeather(mockWeather)
  }, [farmId])

  if (!farm || !stats || !weather) {
    return (
      <div className="farm-bg-farms min-h-screen -m-6 p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading farm details...</p>
          </div>
        </div>
      </div>
    )
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XAF',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'fields', label: 'Fields', icon: Layers },
    { id: 'crops', label: 'Crops', icon: Sprout },
    { id: 'activities', label: 'Activities', icon: Calendar },
    { id: 'inventory', label: 'Inventory', icon: Package },
    { id: 'finance', label: 'Finance', icon: DollarSign },
    { id: 'weather', label: 'Weather', icon: Cloud },
    { id: 'map', label: 'Map Location', icon: MapPin },
    { id: 'reports', label: 'Reports', icon: BarChart3 }
  ]

  return (
    <div className="farm-bg-farms min-h-screen -m-6 p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link 
              to="/farms" 
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Farms
            </Link>
          </div>
        </div>

        {/* Farm Header */}
        <div className="bg-white rounded-lg shadow-lg p-6 card-overlay">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-3">
                <h1 className="text-3xl font-bold text-gray-900">{farm.name}</h1>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  farm.status === 'Active' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {farm.status}
                </span>
              </div>
              <div className="mt-2 space-y-2">
                <div className="flex items-center text-gray-600">
                  <MapPin className="h-4 w-4 mr-2" />
                  {farm.location}
                </div>
                <div className="flex items-center space-x-6 text-sm text-gray-600">
                  <span><strong>Size:</strong> {farm.size} hectares</span>
                  <span><strong>Type:</strong> {farm.type}</span>
                  <span><strong>Owner:</strong> {farm.owner}</span>
                </div>
              </div>
              <p className="mt-3 text-gray-700">{farm.description}</p>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-500">Last Activity</div>
              <div className="text-lg font-semibold text-gray-900">{stats.lastActivity}</div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6 card-overlay">
            <div className="flex items-center">
              <Layers className="h-8 w-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Fields</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalFields}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6 card-overlay">
            <div className="flex items-center">
              <Sprout className="h-8 w-8 text-green-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Crops</p>
                <p className="text-2xl font-bold text-gray-900">{stats.activeCrops}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6 card-overlay">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-emerald-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Monthly Revenue</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.monthlyRevenue)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6 card-overlay">
            <div className="flex items-center">
              <Package className="h-8 w-8 text-purple-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Inventory Items</p>
                <p className="text-2xl font-bold text-gray-900">{stats.inventoryItems}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white rounded-lg shadow card-overlay">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-green-500 text-green-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="h-4 w-4 mr-2" />
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Current Weather Card */}
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold">Current Weather</h3>
                      <p className="text-blue-100">{farm.location}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-3xl font-bold">{weather.current.temperature}°C</div>
                      <div className="text-blue-100">{weather.current.condition}</div>
                    </div>
                  </div>
                  <div className="mt-4 grid grid-cols-3 gap-4">
                    <div className="flex items-center">
                      <Droplets className="h-4 w-4 mr-2" />
                      <span className="text-sm">{weather.current.humidity}% Humidity</span>
                    </div>
                    <div className="flex items-center">
                      <Wind className="h-4 w-4 mr-2" />
                      <span className="text-sm">{weather.current.windSpeed} km/h</span>
                    </div>
                    <div className="flex items-center">
                      <Sun className="h-4 w-4 mr-2" />
                      <span className="text-sm">Good for farming</span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Financial Chart */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Financial Performance</h3>
                    <div className="h-64">
                      <Bar
                        data={{
                          labels: ['Revenue', 'Expenses', 'Profit'],
                          datasets: [{
                            label: 'Amount (CFA)',
                            data: [stats.monthlyRevenue, stats.monthlyExpenses, stats.monthlyRevenue - stats.monthlyExpenses],
                            backgroundColor: ['#10b981', '#ef4444', '#3b82f6'],
                            borderColor: ['#059669', '#dc2626', '#2563eb'],
                            borderWidth: 1
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: { display: false }
                          },
                          scales: {
                            y: {
                              beginAtZero: true,
                              ticks: {
                                callback: function(value) {
                                  return new Intl.NumberFormat('fr-FR', {
                                    style: 'currency',
                                    currency: 'XAF',
                                    minimumFractionDigits: 0,
                                  }).format(value as number)
                                }
                              }
                            }
                          }
                        }}
                      />
                    </div>
                  </div>

                  {/* Crop Distribution */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Crop Distribution</h3>
                    <div className="h-64">
                      <Doughnut
                        data={{
                          labels: ['Maize', 'Cassava', 'Plantain', 'Cocoa', 'Tomatoes'],
                          datasets: [{
                            data: [30, 25, 20, 15, 10],
                            backgroundColor: [
                              '#10b981',
                              '#3b82f6',
                              '#f59e0b',
                              '#8b5cf6',
                              '#ef4444'
                            ],
                            borderWidth: 2,
                            borderColor: '#ffffff'
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              position: 'bottom'
                            }
                          }
                        }}
                      />
                    </div>
                  </div>

                  {/* Monthly Activity Trend */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Activity Trend</h3>
                    <div className="h-64">
                      <Line
                        data={{
                          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                          datasets: [{
                            label: 'Activities Completed',
                            data: [12, 19, 15, 25, 22, 24],
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.4,
                            fill: true
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: { display: false }
                          },
                          scales: {
                            y: { beginAtZero: true }
                          }
                        }}
                      />
                    </div>
                  </div>

                  {/* Recent Activities */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activities</h3>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <Activity className="h-4 w-4 text-blue-500" />
                        <span className="text-sm text-gray-700">Irrigation - Field A</span>
                        <span className="text-xs text-gray-500">2 hours ago</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Sprout className="h-4 w-4 text-green-500" />
                        <span className="text-sm text-gray-700">Planting - Maize</span>
                        <span className="text-xs text-gray-500">1 day ago</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Package className="h-4 w-4 text-purple-500" />
                        <span className="text-sm text-gray-700">Fertilizer Application</span>
                        <span className="text-xs text-gray-500">3 days ago</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <TrendingUp className="h-4 w-4 text-emerald-500" />
                        <span className="text-sm text-gray-700">Harvest - Tomatoes</span>
                        <span className="text-xs text-gray-500">1 week ago</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'fields' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Farm Fields</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[1, 2, 3, 4, 5, 6, 7, 8].map((field) => (
                    <div key={field} className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900">Field {field}</h4>
                      <div className="mt-2 space-y-1 text-sm text-gray-600">
                        <div>Size: {Math.floor(Math.random() * 20) + 5} hectares</div>
                        <div>Crop: {['Maize', 'Cassava', 'Plantain', 'Cocoa'][Math.floor(Math.random() * 4)]}</div>
                        <div>Status: {['Growing', 'Planted', 'Ready for Harvest'][Math.floor(Math.random() * 3)]}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'crops' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Active Crops</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {['Maize', 'Cassava', 'Plantain', 'Cocoa', 'Tomatoes'].map((crop, index) => (
                    <div key={crop} className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900">{crop}</h4>
                      <div className="mt-2 space-y-1 text-sm text-gray-600">
                        <div>Planted: {Math.floor(Math.random() * 30) + 1} days ago</div>
                        <div>Expected Harvest: {Math.floor(Math.random() * 60) + 30} days</div>
                        <div>Health: {['Excellent', 'Good', 'Fair'][Math.floor(Math.random() * 3)]}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'activities' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Farm Activities</h3>
                <div className="space-y-4">
                  {[
                    { activity: 'Irrigation', field: 'Field A', time: '2 hours ago', status: 'Completed' },
                    { activity: 'Fertilizer Application', field: 'Field B', time: '1 day ago', status: 'Completed' },
                    { activity: 'Pest Control', field: 'Field C', time: '2 days ago', status: 'In Progress' },
                    { activity: 'Harvesting', field: 'Field D', time: '3 days ago', status: 'Scheduled' }
                  ].map((activity, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4 flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold text-gray-900">{activity.activity}</h4>
                        <p className="text-sm text-gray-600">{activity.field} • {activity.time}</p>
                      </div>
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                        activity.status === 'Completed' ? 'bg-green-100 text-green-800' :
                        activity.status === 'In Progress' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {activity.status}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'inventory' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Farm Inventory</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[
                    { item: 'Seeds - Maize', quantity: '50 kg', status: 'In Stock' },
                    { item: 'Fertilizer - NPK', quantity: '200 kg', status: 'Low Stock' },
                    { item: 'Pesticide', quantity: '15 liters', status: 'In Stock' },
                    { item: 'Tools - Hoes', quantity: '12 units', status: 'In Stock' },
                    { item: 'Irrigation Pipes', quantity: '500 meters', status: 'In Stock' },
                    { item: 'Seeds - Cassava', quantity: '5 kg', status: 'Out of Stock' }
                  ].map((item, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900">{item.item}</h4>
                      <div className="mt-2 flex items-center justify-between">
                        <span className="text-sm text-gray-600">{item.quantity}</span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.status === 'In Stock' ? 'bg-green-100 text-green-800' :
                          item.status === 'Low Stock' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {item.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'finance' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Financial Details</h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3">Revenue Sources</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Crop Sales:</span>
                        <span className="font-semibold">{formatCurrency(2000000)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Livestock:</span>
                        <span className="font-semibold">{formatCurrency(300000)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Other:</span>
                        <span className="font-semibold">{formatCurrency(200000)}</span>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3">Expenses</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Seeds & Fertilizer:</span>
                        <span className="font-semibold">{formatCurrency(800000)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Labor:</span>
                        <span className="font-semibold">{formatCurrency(600000)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Equipment:</span>
                        <span className="font-semibold">{formatCurrency(400000)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'reports' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Farm Reports</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {[
                    { title: 'Monthly Production Report', date: 'December 2024', status: 'Ready' },
                    { title: 'Financial Summary', date: 'December 2024', status: 'Ready' },
                    { title: 'Crop Health Assessment', date: 'December 2024', status: 'In Progress' },
                    { title: 'Inventory Status Report', date: 'December 2024', status: 'Ready' }
                  ].map((report, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4 flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold text-gray-900">{report.title}</h4>
                        <p className="text-sm text-gray-600">{report.date}</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                          report.status === 'Ready' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {report.status}
                        </span>
                        {report.status === 'Ready' && (
                          <button className="btn-primary text-sm">Download</button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'fields' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Farm Fields</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[1, 2, 3, 4, 5, 6, 7, 8].map((field) => (
                    <div key={field} className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900">Field {field}</h4>
                      <div className="mt-2 space-y-1 text-sm text-gray-600">
                        <div>Size: {Math.floor(Math.random() * 20) + 5} hectares</div>
                        <div>Crop: {['Maize', 'Cassava', 'Plantain', 'Cocoa'][Math.floor(Math.random() * 4)]}</div>
                        <div>Status: {['Growing', 'Planted', 'Ready for Harvest'][Math.floor(Math.random() * 3)]}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'crops' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">Crop Management</h3>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setIsCropModalOpen(true)}
                      className="btn-outline"
                    >
                      Add New Crop
                    </button>
                    <button
                      onClick={() => alert('Crop rotation planning feature - Plan optimal crop sequences for maximum yield and soil health')}
                      className="btn-primary"
                    >
                      Plan Rotation
                    </button>
                  </div>
                </div>

                {/* Crop Statistics */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="bg-green-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <Sprout className="h-8 w-8 text-green-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Active Crops</p>
                        <p className="text-2xl font-bold text-green-600">{stats.activeCrops}</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <Calendar className="h-8 w-8 text-blue-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Planting Season</p>
                        <p className="text-2xl font-bold text-blue-600">3</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-yellow-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <TrendingUp className="h-8 w-8 text-yellow-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Expected Yield</p>
                        <p className="text-2xl font-bold text-yellow-600">2.5T/ha</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-purple-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <Activity className="h-8 w-8 text-purple-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Health Score</p>
                        <p className="text-2xl font-bold text-purple-600">92%</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Crop Performance Charts */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="bg-white rounded-lg shadow p-6">
                    <h4 className="font-semibold text-gray-900 mb-4">Crop Yield Trends</h4>
                    <div className="h-64">
                      <Line
                        data={{
                          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                          datasets: [
                            {
                              label: 'Maize (tons/ha)',
                              data: [2.1, 2.3, 2.5, 2.8, 3.0, 3.2],
                              borderColor: '#eab308',
                              backgroundColor: 'rgba(234, 179, 8, 0.1)',
                              tension: 0.4
                            },
                            {
                              label: 'Cassava (tons/ha)',
                              data: [8.5, 9.0, 9.2, 9.8, 10.1, 10.5],
                              borderColor: '#10b981',
                              backgroundColor: 'rgba(16, 185, 129, 0.1)',
                              tension: 0.4
                            }
                          ]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: { position: 'top' }
                          },
                          scales: {
                            y: { beginAtZero: true }
                          }
                        }}
                      />
                    </div>
                  </div>

                  <div className="bg-white rounded-lg shadow p-6">
                    <h4 className="font-semibold text-gray-900 mb-4">Crop Health Distribution</h4>
                    <div className="h-64">
                      <Doughnut
                        data={{
                          labels: ['Excellent', 'Good', 'Fair', 'Poor'],
                          datasets: [{
                            data: [65, 25, 8, 2],
                            backgroundColor: ['#10b981', '#3b82f6', '#f59e0b', '#ef4444'],
                            borderWidth: 2,
                            borderColor: '#ffffff'
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: { position: 'bottom' }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* Active Crops List */}
                <div className="bg-white rounded-lg shadow p-6">
                  <h4 className="font-semibold text-gray-900 mb-4">Active Crops Details</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {[
                      { name: 'Maize', variety: 'Yellow Dent', planted: '45 days ago', harvest: '75 days', health: 'Excellent', area: '45 ha' },
                      { name: 'Cassava', variety: 'TMS 30572', planted: '120 days ago', harvest: '180 days', health: 'Good', area: '38 ha' },
                      { name: 'Plantain', variety: 'French Horn', planted: '8 months ago', harvest: '4 months', health: 'Excellent', area: '25 ha' },
                      { name: 'Cocoa', variety: 'Trinitario', planted: '2 years ago', harvest: 'Ongoing', health: 'Good', area: '32 ha' },
                      { name: 'Tomatoes', variety: 'Roma', planted: '30 days ago', harvest: '60 days', health: 'Fair', area: '8 ha' }
                    ].map((crop, index) => (
                      <div key={crop.name} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <div className="flex items-center justify-between mb-3">
                          <h5 className="font-semibold text-gray-900">{crop.name}</h5>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            crop.health === 'Excellent' ? 'bg-green-100 text-green-800' :
                            crop.health === 'Good' ? 'bg-blue-100 text-blue-800' :
                            crop.health === 'Fair' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {crop.health}
                          </span>
                        </div>
                        <div className="space-y-2 text-sm text-gray-600">
                          <div><strong>Variety:</strong> {crop.variety}</div>
                          <div><strong>Area:</strong> {crop.area}</div>
                          <div><strong>Planted:</strong> {crop.planted}</div>
                          <div><strong>Expected Harvest:</strong> {crop.harvest}</div>
                        </div>
                        <div className="mt-3 flex space-x-2">
                          <button
                            onClick={() => {
                              setSelectedCrop(crop)
                              alert(`Viewing details for ${crop.name}:\n\nVariety: ${crop.variety}\nArea: ${crop.area}\nPlanted: ${crop.planted}\nExpected Harvest: ${crop.harvest}\nHealth Status: ${crop.health}\n\nDetailed growth tracking, soil conditions, and care history would be displayed here.`)
                            }}
                            className="btn-outline text-xs flex-1"
                          >
                            View Details
                          </button>
                          <button
                            onClick={() => {
                              const newStatus = prompt(`Update health status for ${crop.name}:\n\nCurrent: ${crop.health}\n\nEnter new status (Excellent/Good/Fair/Poor):`, crop.health)
                              if (newStatus) {
                                alert(`${crop.name} health status updated to: ${newStatus}`)
                              }
                            }}
                            className="btn-primary text-xs"
                          >
                            Update Status
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'activities' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">Farm Activities Management</h3>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setIsActivityModalOpen(true)}
                      className="btn-outline"
                    >
                      Schedule Activity
                    </button>
                    <button
                      onClick={() => setIsActivityModalOpen(true)}
                      className="btn-primary"
                    >
                      Add New Activity
                    </button>
                  </div>
                </div>

                {/* Activity Statistics */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <Calendar className="h-8 w-8 text-blue-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Total Activities</p>
                        <p className="text-2xl font-bold text-blue-600">{stats.totalActivities}</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-green-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <Activity className="h-8 w-8 text-green-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Completed</p>
                        <p className="text-2xl font-bold text-green-600">20</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-yellow-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <TrendingUp className="h-8 w-8 text-yellow-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">In Progress</p>
                        <p className="text-2xl font-bold text-yellow-600">3</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-purple-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <MapPin className="h-8 w-8 text-purple-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Scheduled</p>
                        <p className="text-2xl font-bold text-purple-600">1</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Activity Performance Charts */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="bg-white rounded-lg shadow p-6">
                    <h4 className="font-semibold text-gray-900 mb-4">Activity Completion Trends</h4>
                    <div className="h-64">
                      <Line
                        data={{
                          labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6'],
                          datasets: [
                            {
                              label: 'Completed Activities',
                              data: [12, 15, 18, 14, 20, 16],
                              borderColor: '#10b981',
                              backgroundColor: 'rgba(16, 185, 129, 0.1)',
                              tension: 0.4,
                              fill: true
                            },
                            {
                              label: 'Scheduled Activities',
                              data: [15, 18, 20, 16, 22, 18],
                              borderColor: '#3b82f6',
                              backgroundColor: 'rgba(59, 130, 246, 0.1)',
                              tension: 0.4,
                              fill: true
                            }
                          ]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: { position: 'top' }
                          },
                          scales: {
                            y: { beginAtZero: true }
                          }
                        }}
                      />
                    </div>
                  </div>

                  <div className="bg-white rounded-lg shadow p-6">
                    <h4 className="font-semibold text-gray-900 mb-4">Activity Types Distribution</h4>
                    <div className="h-64">
                      <Doughnut
                        data={{
                          labels: ['Irrigation', 'Planting', 'Fertilizing', 'Harvesting', 'Pest Control', 'Maintenance'],
                          datasets: [{
                            data: [25, 20, 18, 15, 12, 10],
                            backgroundColor: [
                              '#3b82f6',
                              '#10b981',
                              '#f59e0b',
                              '#ef4444',
                              '#8b5cf6',
                              '#6b7280'
                            ],
                            borderWidth: 2,
                            borderColor: '#ffffff'
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: { position: 'bottom' }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* Recent Activities */}
                <div className="bg-white rounded-lg shadow p-6">
                  <h4 className="font-semibold text-gray-900 mb-4">Recent Activities</h4>
                  <div className="space-y-4">
                    {[
                      {
                        activity: 'Irrigation System Check',
                        field: 'Field A - Maize Section',
                        time: '2 hours ago',
                        status: 'Completed',
                        assignee: 'Paul Tabi',
                        priority: 'High',
                        duration: '3 hours'
                      },
                      {
                        activity: 'Fertilizer Application',
                        field: 'Field B - Cassava Section',
                        time: '1 day ago',
                        status: 'Completed',
                        assignee: 'Marie Atangana',
                        priority: 'Medium',
                        duration: '5 hours'
                      },
                      {
                        activity: 'Pest Control Treatment',
                        field: 'Field C - Plantain Section',
                        time: '2 days ago',
                        status: 'In Progress',
                        assignee: 'Ibrahim Moussa',
                        priority: 'High',
                        duration: '4 hours'
                      },
                      {
                        activity: 'Soil Testing',
                        field: 'Field D - Cocoa Section',
                        time: '3 days ago',
                        status: 'Scheduled',
                        assignee: 'Ngozi Mbah',
                        priority: 'Low',
                        duration: '2 hours'
                      }
                    ].map((activity, index) => (
                      <div key={index} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex-1">
                            <h5 className="font-semibold text-gray-900">{activity.activity}</h5>
                            <p className="text-sm text-gray-600">{activity.field} • {activity.time}</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              activity.priority === 'High' ? 'bg-red-100 text-red-800' :
                              activity.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-green-100 text-green-800'
                            }`}>
                              {activity.priority}
                            </span>
                            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                              activity.status === 'Completed' ? 'bg-green-100 text-green-800' :
                              activity.status === 'In Progress' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-blue-100 text-blue-800'
                            }`}>
                              {activity.status}
                            </span>
                          </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                          <div><strong>Assignee:</strong> {activity.assignee}</div>
                          <div><strong>Duration:</strong> {activity.duration}</div>
                          <div><strong>Status:</strong> {activity.status}</div>
                        </div>
                        <div className="mt-3 flex space-x-2">
                          <button
                            onClick={() => {
                              alert(`Activity Details:\n\nActivity: ${activity.activity}\nField: ${activity.field}\nAssignee: ${activity.assignee}\nPriority: ${activity.priority}\nDuration: ${activity.duration}\nStatus: ${activity.status}\nScheduled: ${activity.time}\n\nDetailed activity logs, equipment used, and progress notes would be displayed here.`)
                            }}
                            className="btn-outline text-xs"
                          >
                            View Details
                          </button>
                          <button
                            onClick={() => {
                              const newStatus = prompt(`Update status for ${activity.activity}:\n\nCurrent: ${activity.status}\n\nEnter new status (Completed/In Progress/Scheduled/Cancelled):`, activity.status)
                              if (newStatus) {
                                alert(`${activity.activity} status updated to: ${newStatus}`)
                              }
                            }}
                            className="btn-primary text-xs"
                          >
                            Update Status
                          </button>
                          {activity.status === 'Scheduled' && (
                            <button
                              onClick={() => {
                                if (confirm(`Cancel ${activity.activity}?`)) {
                                  alert(`${activity.activity} has been cancelled`)
                                }
                              }}
                              className="btn-outline text-xs text-red-600"
                            >
                              Cancel
                            </button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'inventory' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Farm Inventory</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[
                    { item: 'Seeds - Maize', quantity: '50 kg', status: 'In Stock' },
                    { item: 'Fertilizer - NPK', quantity: '200 kg', status: 'Low Stock' },
                    { item: 'Pesticide', quantity: '15 liters', status: 'In Stock' },
                    { item: 'Tools - Hoes', quantity: '12 units', status: 'In Stock' },
                    { item: 'Irrigation Pipes', quantity: '500 meters', status: 'In Stock' },
                    { item: 'Seeds - Cassava', quantity: '5 kg', status: 'Out of Stock' }
                  ].map((item, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900">{item.item}</h4>
                      <div className="mt-2 flex items-center justify-between">
                        <span className="text-sm text-gray-600">{item.quantity}</span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.status === 'In Stock' ? 'bg-green-100 text-green-800' :
                          item.status === 'Low Stock' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {item.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'finance' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">Financial Management</h3>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => {
                        // Export financial data as CSV
                        const csvData = [
                          ['Month', 'Revenue', 'Expenses', 'Profit'],
                          ['January', '2200000', '1600000', '600000'],
                          ['February', '2400000', '1700000', '700000'],
                          ['March', '2100000', '1500000', '600000'],
                          ['April', '2500000', '1800000', '700000'],
                          ['May', '2300000', '1650000', '650000'],
                          ['June', '2500000', '1800000', '700000']
                        ]
                        const csvContent = csvData.map(row => row.join(',')).join('\n')
                        const blob = new Blob([csvContent], { type: 'text/csv' })
                        const url = window.URL.createObjectURL(blob)
                        const a = document.createElement('a')
                        a.href = url
                        a.download = `${farm.name}_financial_data.csv`
                        a.click()
                        window.URL.revokeObjectURL(url)
                      }}
                      className="btn-outline"
                    >
                      Export Data
                    </button>
                    <button
                      onClick={() => setIsTransactionModalOpen(true)}
                      className="btn-primary"
                    >
                      Add Transaction
                    </button>
                  </div>
                </div>

                {/* Financial Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="bg-green-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <TrendingUp className="h-8 w-8 text-green-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                        <p className="text-2xl font-bold text-green-600">{formatCurrency(stats.monthlyRevenue)}</p>
                        <p className="text-xs text-green-500">+12% from last month</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-red-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <DollarSign className="h-8 w-8 text-red-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Total Expenses</p>
                        <p className="text-2xl font-bold text-red-600">{formatCurrency(stats.monthlyExpenses)}</p>
                        <p className="text-xs text-red-500">+5% from last month</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <BarChart3 className="h-8 w-8 text-blue-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Net Profit</p>
                        <p className="text-2xl font-bold text-blue-600">{formatCurrency(stats.monthlyRevenue - stats.monthlyExpenses)}</p>
                        <p className="text-xs text-blue-500">+28% from last month</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-purple-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <Activity className="h-8 w-8 text-purple-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Profit Margin</p>
                        <p className="text-2xl font-bold text-purple-600">28%</p>
                        <p className="text-xs text-purple-500">+3% from last month</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Main Financial Charts */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Revenue vs Expenses Trend */}
                  <div className="bg-white rounded-lg shadow p-6">
                    <h4 className="font-semibold text-gray-900 mb-4">Revenue vs Expenses Trend</h4>
                    <div className="h-80">
                      <Line
                        data={{
                          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                          datasets: [
                            {
                              label: 'Revenue (CFA)',
                              data: [2200000, 2400000, 2100000, 2500000, 2300000, 2500000, 2700000, 2600000, 2800000, 2900000, 2750000, 3000000],
                              borderColor: '#10b981',
                              backgroundColor: 'rgba(16, 185, 129, 0.1)',
                              tension: 0.4,
                              fill: true
                            },
                            {
                              label: 'Expenses (CFA)',
                              data: [1600000, 1700000, 1500000, 1800000, 1650000, 1800000, 1900000, 1850000, 1950000, 2000000, 1900000, 2100000],
                              borderColor: '#ef4444',
                              backgroundColor: 'rgba(239, 68, 68, 0.1)',
                              tension: 0.4,
                              fill: true
                            }
                          ]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: { position: 'top' },
                            tooltip: {
                              callbacks: {
                                label: function(context) {
                                  return context.dataset.label + ': ' + new Intl.NumberFormat('fr-FR', {
                                    style: 'currency',
                                    currency: 'XAF',
                                    minimumFractionDigits: 0,
                                  }).format(context.parsed.y)
                                }
                              }
                            }
                          },
                          scales: {
                            y: {
                              beginAtZero: true,
                              ticks: {
                                callback: function(value) {
                                  return new Intl.NumberFormat('fr-FR', {
                                    style: 'currency',
                                    currency: 'XAF',
                                    minimumFractionDigits: 0,
                                  }).format(value as number)
                                }
                              }
                            }
                          }
                        }}
                      />
                    </div>
                  </div>

                  {/* Monthly Profit Analysis */}
                  <div className="bg-white rounded-lg shadow p-6">
                    <h4 className="font-semibold text-gray-900 mb-4">Monthly Profit Analysis</h4>
                    <div className="h-80">
                      <Bar
                        data={{
                          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                          datasets: [
                            {
                              label: 'Profit (CFA)',
                              data: [600000, 700000, 600000, 700000, 650000, 700000, 800000, 750000, 850000, 900000, 850000, 900000],
                              backgroundColor: [
                                '#10b981', '#10b981', '#10b981', '#10b981', '#10b981', '#10b981',
                                '#10b981', '#10b981', '#10b981', '#10b981', '#10b981', '#10b981'
                              ],
                              borderColor: '#059669',
                              borderWidth: 1
                            }
                          ]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: { display: false },
                            tooltip: {
                              callbacks: {
                                label: function(context) {
                                  return 'Profit: ' + new Intl.NumberFormat('fr-FR', {
                                    style: 'currency',
                                    currency: 'XAF',
                                    minimumFractionDigits: 0,
                                  }).format(context.parsed.y)
                                }
                              }
                            }
                          },
                          scales: {
                            y: {
                              beginAtZero: true,
                              ticks: {
                                callback: function(value) {
                                  return new Intl.NumberFormat('fr-FR', {
                                    style: 'currency',
                                    currency: 'XAF',
                                    minimumFractionDigits: 0,
                                  }).format(value as number)
                                }
                              }
                            }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* Secondary Financial Charts */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

                  {/* Expense Breakdown */}
                  <div className="bg-white rounded-lg shadow p-6">
                    <h4 className="font-semibold text-gray-900 mb-4">Expense Breakdown</h4>
                    <div className="h-80">
                      <Doughnut
                        data={{
                          labels: ['Seeds & Fertilizer', 'Labor Costs', 'Equipment & Maintenance', 'Utilities', 'Transportation', 'Other'],
                          datasets: [{
                            data: [800000, 600000, 400000, 200000, 150000, 100000],
                            backgroundColor: [
                              '#ef4444',
                              '#f59e0b',
                              '#3b82f6',
                              '#8b5cf6',
                              '#10b981',
                              '#6b7280'
                            ],
                            borderWidth: 3,
                            borderColor: '#ffffff'
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              position: 'bottom',
                              labels: {
                                padding: 20,
                                usePointStyle: true
                              }
                            },
                            tooltip: {
                              callbacks: {
                                label: function(context) {
                                  const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0)
                                  const percentage = ((context.parsed / total) * 100).toFixed(1)
                                  return context.label + ': ' + new Intl.NumberFormat('fr-FR', {
                                    style: 'currency',
                                    currency: 'XAF',
                                    minimumFractionDigits: 0,
                                  }).format(context.parsed) + ` (${percentage}%)`
                                }
                              }
                            }
                          }
                        }}
                      />
                    </div>
                  </div>

                  {/* Revenue Sources */}
                  <div className="bg-white rounded-lg shadow p-6">
                    <h4 className="font-semibold text-gray-900 mb-4">Revenue Sources</h4>
                    <div className="h-80">
                      <Doughnut
                        data={{
                          labels: ['Crop Sales', 'Livestock', 'Processed Products', 'Equipment Rental', 'Consulting', 'Other'],
                          datasets: [{
                            data: [2000000, 300000, 200000, 150000, 100000, 50000],
                            backgroundColor: [
                              '#10b981',
                              '#3b82f6',
                              '#f59e0b',
                              '#8b5cf6',
                              '#ef4444',
                              '#6b7280'
                            ],
                            borderWidth: 3,
                            borderColor: '#ffffff'
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              position: 'bottom',
                              labels: {
                                padding: 20,
                                usePointStyle: true
                              }
                            },
                            tooltip: {
                              callbacks: {
                                label: function(context) {
                                  const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0)
                                  const percentage = ((context.parsed / total) * 100).toFixed(1)
                                  return context.label + ': ' + new Intl.NumberFormat('fr-FR', {
                                    style: 'currency',
                                    currency: 'XAF',
                                    minimumFractionDigits: 0,
                                  }).format(context.parsed) + ` (${percentage}%)`
                                }
                              }
                            }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* Financial Performance Metrics */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

                  {/* Revenue Sources */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3">Revenue Sources</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Crop Sales:</span>
                        <span className="font-semibold">{formatCurrency(2000000)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{width: '80%'}}></div>
                      </div>

                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Livestock:</span>
                        <span className="font-semibold">{formatCurrency(300000)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-blue-500 h-2 rounded-full" style={{width: '12%'}}></div>
                      </div>

                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Other:</span>
                        <span className="font-semibold">{formatCurrency(200000)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-purple-500 h-2 rounded-full" style={{width: '8%'}}></div>
                      </div>
                    </div>
                  </div>

                  {/* Expense Details */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3">Expense Details</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Seeds & Fertilizer:</span>
                        <span className="font-semibold">{formatCurrency(800000)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-red-500 h-2 rounded-full" style={{width: '44%'}}></div>
                      </div>

                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Labor:</span>
                        <span className="font-semibold">{formatCurrency(600000)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-yellow-500 h-2 rounded-full" style={{width: '33%'}}></div>
                      </div>

                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Equipment:</span>
                        <span className="font-semibold">{formatCurrency(400000)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-blue-500 h-2 rounded-full" style={{width: '22%'}}></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Additional Financial Analytics */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Cash Flow Analysis */}
                  <div className="bg-white rounded-lg shadow p-6">
                    <h4 className="font-semibold text-gray-900 mb-4">Cash Flow Analysis</h4>
                    <div className="h-64">
                      <Bar
                        data={{
                          labels: ['Q1', 'Q2', 'Q3', 'Q4'],
                          datasets: [
                            {
                              label: 'Cash Inflow',
                              data: [7500000, 8200000, 9100000, 8800000],
                              backgroundColor: '#10b981',
                              borderColor: '#059669',
                              borderWidth: 1
                            },
                            {
                              label: 'Cash Outflow',
                              data: [5400000, 5800000, 6200000, 6000000],
                              backgroundColor: '#ef4444',
                              borderColor: '#dc2626',
                              borderWidth: 1
                            }
                          ]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: { position: 'top' },
                            tooltip: {
                              callbacks: {
                                label: function(context) {
                                  return context.dataset.label + ': ' + new Intl.NumberFormat('fr-FR', {
                                    style: 'currency',
                                    currency: 'XAF',
                                    minimumFractionDigits: 0,
                                  }).format(context.parsed.y)
                                }
                              }
                            }
                          },
                          scales: {
                            y: {
                              beginAtZero: true,
                              ticks: {
                                callback: function(value) {
                                  return new Intl.NumberFormat('fr-FR', {
                                    style: 'currency',
                                    currency: 'XAF',
                                    minimumFractionDigits: 0,
                                  }).format(value as number)
                                }
                              }
                            }
                          }
                        }}
                      />
                    </div>
                  </div>

                  {/* ROI and Profitability */}
                  <div className="bg-white rounded-lg shadow p-6">
                    <h4 className="font-semibold text-gray-900 mb-4">ROI & Profitability Trends</h4>
                    <div className="h-64">
                      <Line
                        data={{
                          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                          datasets: [
                            {
                              label: 'ROI (%)',
                              data: [15, 18, 16, 22, 20, 25],
                              borderColor: '#8b5cf6',
                              backgroundColor: 'rgba(139, 92, 246, 0.1)',
                              tension: 0.4
                            },
                            {
                              label: 'Profit Margin (%)',
                              data: [22, 25, 23, 28, 26, 30],
                              borderColor: '#10b981',
                              backgroundColor: 'rgba(16, 185, 129, 0.1)',
                              tension: 0.4
                            }
                          ]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: { position: 'top' }
                          },
                          scales: {
                            y: {
                              beginAtZero: true,
                              max: 50,
                              ticks: {
                                callback: function(value) {
                                  return value + '%'
                                }
                              }
                            }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* Financial KPIs */}
                <div className="bg-white rounded-lg shadow p-6">
                  <h4 className="font-semibold text-gray-900 mb-4">Key Financial Indicators</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">{formatCurrency(stats.monthlyRevenue)}</div>
                      <div className="text-sm text-gray-600 mt-1">Monthly Revenue</div>
                      <div className="text-xs text-green-500 mt-1">↗ +12% vs last month</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">28%</div>
                      <div className="text-sm text-gray-600 mt-1">Profit Margin</div>
                      <div className="text-xs text-blue-500 mt-1">↗ +3% vs last month</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-purple-600">35%</div>
                      <div className="text-sm text-gray-600 mt-1">ROI</div>
                      <div className="text-xs text-purple-500 mt-1">↗ +5% vs last month</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-orange-600">{formatCurrency(Math.floor(stats.monthlyRevenue / stats.totalFields))}</div>
                      <div className="text-sm text-gray-600 mt-1">Revenue per Field</div>
                      <div className="text-xs text-orange-500 mt-1">↗ +8% vs last month</div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'weather' && (
              <div className="space-y-6">
                {/* Current Weather Details */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="bg-blue-50 rounded-lg p-4 text-center">
                    <Thermometer className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900">{weather.current.temperature}°C</div>
                    <div className="text-sm text-gray-600">Temperature</div>
                  </div>
                  <div className="bg-green-50 rounded-lg p-4 text-center">
                    <Droplets className="h-8 w-8 text-green-500 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900">{weather.current.humidity}%</div>
                    <div className="text-sm text-gray-600">Humidity</div>
                  </div>
                  <div className="bg-yellow-50 rounded-lg p-4 text-center">
                    <Wind className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900">{weather.current.windSpeed}</div>
                    <div className="text-sm text-gray-600">Wind (km/h)</div>
                  </div>
                  <div className="bg-purple-50 rounded-lg p-4 text-center">
                    <Cloud className="h-8 w-8 text-purple-500 mx-auto mb-2" />
                    <div className="text-lg font-bold text-gray-900">{weather.current.condition}</div>
                    <div className="text-sm text-gray-600">Condition</div>
                  </div>
                </div>

                {/* 5-Day Forecast */}
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">5-Day Forecast</h3>
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                    {weather.forecast.map((day, index) => (
                      <div key={index} className="bg-white rounded-lg p-4 text-center">
                        <div className="font-semibold text-gray-900">{day.day}</div>
                        <div className="text-2xl my-2">
                          {day.condition === 'Sunny' ? '☀️' :
                           day.condition === 'Cloudy' ? '☁️' :
                           day.condition === 'Rainy' ? '🌧️' : '⛅'}
                        </div>
                        <div className="text-sm text-gray-600">{day.condition}</div>
                        <div className="flex justify-between mt-2 text-sm">
                          <span className="font-semibold">{day.high}°</span>
                          <span className="text-gray-500">{day.low}°</span>
                        </div>
                        <div className="text-xs text-blue-600 mt-1">
                          {day.precipitation}% rain
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Weather Impact on Farming */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Farming Recommendations</h3>
                    <div className="space-y-3">
                      <div className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                        <div>
                          <div className="font-medium text-gray-900">Good conditions for irrigation</div>
                          <div className="text-sm text-gray-600">Current humidity levels are optimal</div>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                        <div>
                          <div className="font-medium text-gray-900">Monitor for rain on Wednesday</div>
                          <div className="text-sm text-gray-600">80% chance of precipitation</div>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                        <div>
                          <div className="font-medium text-gray-900">Ideal planting weather ahead</div>
                          <div className="text-sm text-gray-600">Thursday-Friday look perfect</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Weather Alerts</h3>
                    <div className="space-y-3">
                      <div className="bg-yellow-100 border border-yellow-300 rounded-lg p-3">
                        <div className="flex items-center">
                          <AlertCircle className="h-4 w-4 text-yellow-600 mr-2" />
                          <span className="font-medium text-yellow-800">Rain Expected</span>
                        </div>
                        <p className="text-sm text-yellow-700 mt-1">
                          Heavy rain expected Wednesday. Consider postponing outdoor activities.
                        </p>
                      </div>
                      <div className="bg-green-100 border border-green-300 rounded-lg p-3">
                        <div className="flex items-center">
                          <Sun className="h-4 w-4 text-green-600 mr-2" />
                          <span className="font-medium text-green-800">Optimal Growing Conditions</span>
                        </div>
                        <p className="text-sm text-green-700 mt-1">
                          Temperature and humidity levels are ideal for crop growth.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'map' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">Farm Location & Mapping</h3>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => {
                        const coords = {
                          'Bamenda, North West Region': [5.9631, 10.1591],
                          'Kribi, South Region': [2.9373, 9.9073],
                          'Maroua, Far North Region': [10.5906, 14.3158],
                          'Bafoussam, West Region': [5.4737, 10.4178],
                          'Bertoua, East Region': [4.5774, 13.6848]
                        }
                        const [lat, lng] = coords[farm.location as keyof typeof coords] || [4.0511, 9.7679]
                        window.open(`https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`, '_blank')
                      }}
                      className="btn-outline"
                    >
                      <MapPin className="h-4 w-4 mr-2" />
                      Get Directions
                    </button>
                    <button
                      onClick={() => {
                        const coords = {
                          'Bamenda, North West Region': [5.9631, 10.1591],
                          'Kribi, South Region': [2.9373, 9.9073],
                          'Maroua, Far North Region': [10.5906, 14.3158],
                          'Bafoussam, West Region': [5.4737, 10.4178],
                          'Bertoua, East Region': [4.5774, 13.6848]
                        }
                        const [lat, lng] = coords[farm.location as keyof typeof coords] || [4.0511, 9.7679]
                        window.open(`https://www.google.com/maps/@${lat},${lng},18z/data=!3m1!1e3`, '_blank')
                      }}
                      className="btn-primary"
                    >
                      View Satellite
                    </button>
                  </div>
                </div>

                {/* Farm Location Info */}
                <div className="bg-white rounded-lg shadow p-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Farm Details</h4>
                      <div className="space-y-1 text-sm">
                        <p><strong>Name:</strong> {farm.name}</p>
                        <p><strong>Location:</strong> {farm.location}</p>
                        <p><strong>Size:</strong> {farm.size} hectares</p>
                        <p><strong>Type:</strong> {farm.type}</p>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Coordinates</h4>
                      <div className="space-y-1 text-sm">
                        <p><strong>Latitude:</strong> {(() => {
                          const coords = {
                            'Bamenda, North West Region': '5.9631°N',
                            'Kribi, South Region': '2.9373°N',
                            'Maroua, Far North Region': '10.5906°N',
                            'Bafoussam, West Region': '5.4737°N',
                            'Bertoua, East Region': '4.5774°N'
                          }
                          return coords[farm.location as keyof typeof coords] || '4.0511°N'
                        })()}</p>
                        <p><strong>Longitude:</strong> {(() => {
                          const coords = {
                            'Bamenda, North West Region': '10.1591°E',
                            'Kribi, South Region': '9.9073°E',
                            'Maroua, Far North Region': '14.3158°E',
                            'Bafoussam, West Region': '10.4178°E',
                            'Bertoua, East Region': '13.6848°E'
                          }
                          return coords[farm.location as keyof typeof coords] || '9.7679°E'
                        })()}</p>
                        <p><strong>Elevation:</strong> ~{Math.floor(Math.random() * 500 + 200)}m</p>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Contact Info</h4>
                      <div className="space-y-1 text-sm">
                        <p><strong>Owner:</strong> {farm.owner}</p>
                        <p><strong>Contact:</strong> {farm.contact}</p>
                        <p><strong>Status:</strong>
                          <span className={`ml-1 px-2 py-1 rounded-full text-xs font-medium ${
                            farm.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {farm.status}
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Interactive Map */}
                  <div className="h-96 rounded-lg overflow-hidden border border-gray-200">
                    <FarmDetailMap
                      farm={{
                        id: parseInt(farm.id),
                        name: farm.name,
                        coordinates: {
                          latitude: (() => {
                            const coords = {
                              'Bamenda, North West Region': 5.9631,
                              'Kribi, South Region': 2.9373,
                              'Maroua, Far North Region': 10.5906,
                              'Bafoussam, West Region': 5.4737,
                              'Bertoua, East Region': 4.5774
                            }
                            return coords[farm.location as keyof typeof coords] || 4.0511
                          })(),
                          longitude: (() => {
                            const coords = {
                              'Bamenda, North West Region': 10.1591,
                              'Kribi, South Region': 9.9073,
                              'Maroua, Far North Region': 14.3158,
                              'Bafoussam, West Region': 10.4178,
                              'Bertoua, East Region': 13.6848
                            }
                            return coords[farm.location as keyof typeof coords] || 9.7679
                          })()
                        },
                        size: farm.size,
                        sizeUnit: 'hectares',
                        address: farm.location
                      }}
                      height="400px"
                    />
                  </div>
                </div>

                {/* Field Distribution */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center mb-4">
                      <Layers className="h-6 w-6 text-blue-500 mr-3" />
                      <h4 className="font-semibold text-gray-900">Field Distribution</h4>
                    </div>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Total Fields:</span>
                        <span className="font-semibold">8 fields</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Cultivated Area:</span>
                        <span className="font-semibold">{Math.floor(farm.size * 0.8)} hectares</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Fallow Land:</span>
                        <span className="font-semibold">{Math.floor(farm.size * 0.2)} hectares</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center mb-4">
                      <Sprout className="h-6 w-6 text-green-500 mr-3" />
                      <h4 className="font-semibold text-gray-900">Crop Coverage</h4>
                    </div>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-yellow-400 rounded mr-2"></div>
                          <span className="text-sm text-gray-600">Maize:</span>
                        </div>
                        <span className="font-semibold">30%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-green-400 rounded mr-2"></div>
                          <span className="text-sm text-gray-600">Cassava:</span>
                        </div>
                        <span className="font-semibold">25%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-orange-400 rounded mr-2"></div>
                          <span className="text-sm text-gray-600">Plantain:</span>
                        </div>
                        <span className="font-semibold">20%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-blue-400 rounded mr-2"></div>
                          <span className="text-sm text-gray-600">Cocoa:</span>
                        </div>
                        <span className="font-semibold">15%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-purple-400 rounded mr-2"></div>
                          <span className="text-sm text-gray-600">Other:</span>
                        </div>
                        <span className="font-semibold">10%</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center mb-4">
                      <MapPin className="h-6 w-6 text-red-500 mr-3" />
                      <h4 className="font-semibold text-gray-900">Infrastructure</h4>
                    </div>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Storage Buildings:</span>
                        <span className="font-semibold">3 units</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Water Sources:</span>
                        <span className="font-semibold">2 boreholes</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Access Roads:</span>
                        <span className="font-semibold">2.5 km</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'reports' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">Farm Reports & Analytics</h3>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setIsScheduleModalOpen(true)}
                      className="btn-outline"
                    >
                      Schedule Report
                    </button>
                    <button className="btn-primary">Generate New Report</button>
                  </div>
                </div>

                {/* Report Analytics Overview */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <BarChart3 className="h-8 w-8 text-blue-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Total Reports</p>
                        <p className="text-2xl font-bold text-blue-600">24</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-green-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <Activity className="h-8 w-8 text-green-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">This Month</p>
                        <p className="text-2xl font-bold text-green-600">6</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-yellow-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <Calendar className="h-8 w-8 text-yellow-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Scheduled</p>
                        <p className="text-2xl font-bold text-yellow-600">3</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-purple-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <TrendingUp className="h-8 w-8 text-purple-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Automated</p>
                        <p className="text-2xl font-bold text-purple-600">8</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Report Generation Trends */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="bg-white rounded-lg shadow p-6">
                    <h4 className="font-semibold text-gray-900 mb-4">Report Generation Trends</h4>
                    <div className="h-64">
                      <Line
                        data={{
                          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                          datasets: [
                            {
                              label: 'Reports Generated',
                              data: [8, 12, 10, 15, 18, 20, 22, 25, 23, 28, 26, 30],
                              borderColor: '#3b82f6',
                              backgroundColor: 'rgba(59, 130, 246, 0.1)',
                              tension: 0.4,
                              fill: true
                            }
                          ]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: { display: false }
                          },
                          scales: {
                            y: { beginAtZero: true }
                          }
                        }}
                      />
                    </div>
                  </div>

                  <div className="bg-white rounded-lg shadow p-6">
                    <h4 className="font-semibold text-gray-900 mb-4">Report Types Distribution</h4>
                    <div className="h-64">
                      <Doughnut
                        data={{
                          labels: ['Financial', 'Activity', 'Inventory', 'Crop', 'Production', 'Weather'],
                          datasets: [{
                            data: [25, 20, 15, 18, 12, 10],
                            backgroundColor: [
                              '#10b981',
                              '#3b82f6',
                              '#8b5cf6',
                              '#f59e0b',
                              '#ef4444',
                              '#6b7280'
                            ],
                            borderWidth: 2,
                            borderColor: '#ffffff'
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: { position: 'bottom' }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* Scheduled Reports */}
                <div className="bg-white rounded-lg shadow p-6">
                  <h4 className="font-semibold text-gray-900 mb-4">Scheduled Reports</h4>
                  <div className="space-y-3">
                    {[
                      { name: 'Weekly Financial Summary', frequency: 'Every Monday', nextRun: '2024-12-16', status: 'Active' },
                      { name: 'Monthly Production Report', frequency: 'First of month', nextRun: '2025-01-01', status: 'Active' },
                      { name: 'Quarterly Crop Analysis', frequency: 'Every 3 months', nextRun: '2025-01-15', status: 'Paused' }
                    ].map((schedule, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <h5 className="font-medium text-gray-900">{schedule.name}</h5>
                          <p className="text-sm text-gray-600">{schedule.frequency} • Next: {schedule.nextRun}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            schedule.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {schedule.status}
                          </span>
                          <button className="btn-outline text-sm">Edit</button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* Financial Report */}
                  <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                    <div className="flex items-center mb-4">
                      <DollarSign className="h-8 w-8 text-green-500" />
                      <div className="ml-3">
                        <h3 className="text-lg font-semibold text-gray-900">Financial Report</h3>
                        <p className="text-sm text-gray-600">Revenue, expenses, and profitability analysis</p>
                      </div>
                    </div>
                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Period:</span>
                        <span className="font-medium">December 2024</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Status:</span>
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">Ready</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          setSelectedReport({
                            title: 'Financial Report',
                            type: 'financial',
                            period: 'December 2024',
                            data: {
                              revenue: stats.monthlyRevenue,
                              expenses: stats.monthlyExpenses,
                              profit: stats.monthlyRevenue - stats.monthlyExpenses
                            }
                          })
                          setIsReportModalOpen(true)
                        }}
                        className="btn-outline flex-1"
                      >
                        View Report
                      </button>
                      <button className="btn-primary">Download</button>
                    </div>
                  </div>

                  {/* Activity Report */}
                  <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                    <div className="flex items-center mb-4">
                      <Calendar className="h-8 w-8 text-blue-500" />
                      <div className="ml-3">
                        <h3 className="text-lg font-semibold text-gray-900">Activity Report</h3>
                        <p className="text-sm text-gray-600">Farm activities and task completion</p>
                      </div>
                    </div>
                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Period:</span>
                        <span className="font-medium">December 2024</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Status:</span>
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">Ready</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          setSelectedReport({
                            title: 'Activity Report',
                            type: 'activity',
                            period: 'December 2024',
                            data: {
                              totalActivities: stats.totalActivities,
                              completedActivities: 20,
                              pendingActivities: 4
                            }
                          })
                          setIsReportModalOpen(true)
                        }}
                        className="btn-outline flex-1"
                      >
                        View Report
                      </button>
                      <button className="btn-primary">Download</button>
                    </div>
                  </div>

                  {/* Inventory Report */}
                  <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                    <div className="flex items-center mb-4">
                      <Package className="h-8 w-8 text-purple-500" />
                      <div className="ml-3">
                        <h3 className="text-lg font-semibold text-gray-900">Inventory Report</h3>
                        <p className="text-sm text-gray-600">Stock levels and inventory management</p>
                      </div>
                    </div>
                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Period:</span>
                        <span className="font-medium">December 2024</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Status:</span>
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">Ready</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          setSelectedReport({
                            title: 'Inventory Report',
                            type: 'inventory',
                            period: 'December 2024',
                            data: {
                              totalItems: stats.inventoryItems,
                              inStock: 35,
                              lowStock: 8,
                              outOfStock: 2
                            }
                          })
                          setIsReportModalOpen(true)
                        }}
                        className="btn-outline flex-1"
                      >
                        View Report
                      </button>
                      <button className="btn-primary">Download</button>
                    </div>
                  </div>

                  {/* Crop Report */}
                  <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                    <div className="flex items-center mb-4">
                      <Sprout className="h-8 w-8 text-green-600" />
                      <div className="ml-3">
                        <h3 className="text-lg font-semibold text-gray-900">Crop Report</h3>
                        <p className="text-sm text-gray-600">Crop health and yield analysis</p>
                      </div>
                    </div>
                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Period:</span>
                        <span className="font-medium">December 2024</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Status:</span>
                        <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">In Progress</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          setSelectedReport({
                            title: 'Crop Report',
                            type: 'crop',
                            period: 'December 2024',
                            data: {
                              activeCrops: stats.activeCrops,
                              healthyPlants: 85,
                              diseased: 10,
                              expectedYield: '2.5 tons/hectare'
                            }
                          })
                          setIsReportModalOpen(true)
                        }}
                        className="btn-outline flex-1"
                      >
                        View Report
                      </button>
                      <button className="btn-primary" disabled>Download</button>
                    </div>
                  </div>

                  {/* Production Report */}
                  <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                    <div className="flex items-center mb-4">
                      <TrendingUp className="h-8 w-8 text-orange-500" />
                      <div className="ml-3">
                        <h3 className="text-lg font-semibold text-gray-900">Production Report</h3>
                        <p className="text-sm text-gray-600">Overall farm productivity metrics</p>
                      </div>
                    </div>
                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Period:</span>
                        <span className="font-medium">December 2024</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Status:</span>
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">Ready</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          setSelectedReport({
                            title: 'Production Report',
                            type: 'production',
                            period: 'December 2024',
                            data: {
                              totalFields: stats.totalFields,
                              productivity: '95%',
                              yield: '3.2 tons/hectare',
                              efficiency: 'High'
                            }
                          })
                          setIsReportModalOpen(true)
                        }}
                        className="btn-outline flex-1"
                      >
                        View Report
                      </button>
                      <button className="btn-primary">Download</button>
                    </div>
                  </div>

                  {/* Weather Impact Report */}
                  <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                    <div className="flex items-center mb-4">
                      <Cloud className="h-8 w-8 text-blue-400" />
                      <div className="ml-3">
                        <h3 className="text-lg font-semibold text-gray-900">Weather Impact Report</h3>
                        <p className="text-sm text-gray-600">Weather effects on farm operations</p>
                      </div>
                    </div>
                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Period:</span>
                        <span className="font-medium">December 2024</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Status:</span>
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">Ready</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          setSelectedReport({
                            title: 'Weather Impact Report',
                            type: 'weather',
                            period: 'December 2024',
                            data: {
                              avgTemperature: weather.current.temperature,
                              rainfall: '120mm',
                              optimalDays: 22,
                              weatherAlerts: 3
                            }
                          })
                          setIsReportModalOpen(true)
                        }}
                        className="btn-outline flex-1"
                      >
                        View Report
                      </button>
                      <button className="btn-primary">Download</button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Crop Modal */}
        {isCropModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-md w-full">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-bold text-gray-900">Add New Crop</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Crop Name</label>
                    <select className="w-full border border-gray-300 rounded-lg px-3 py-2">
                      <option>Maize</option>
                      <option>Cassava</option>
                      <option>Plantain</option>
                      <option>Cocoa</option>
                      <option>Tomatoes</option>
                      <option>Yam</option>
                      <option>Sweet Potato</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Variety</label>
                    <input type="text" placeholder="e.g., Yellow Dent, TMS 30572" className="w-full border border-gray-300 rounded-lg px-3 py-2" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Field/Area (hectares)</label>
                    <input type="number" placeholder="0" className="w-full border border-gray-300 rounded-lg px-3 py-2" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Planting Date</label>
                    <input type="date" className="w-full border border-gray-300 rounded-lg px-3 py-2" />
                  </div>
                </div>
              </div>
              <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
                <button onClick={() => setIsCropModalOpen(false)} className="btn-outline">Cancel</button>
                <button
                  onClick={() => {
                    alert('New crop added successfully!')
                    setIsCropModalOpen(false)
                  }}
                  className="btn-primary"
                >
                  Add Crop
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Activity Modal */}
        {isActivityModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-md w-full">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-bold text-gray-900">Schedule New Activity</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Activity Type</label>
                    <select className="w-full border border-gray-300 rounded-lg px-3 py-2">
                      <option>Irrigation</option>
                      <option>Planting</option>
                      <option>Fertilizing</option>
                      <option>Pest Control</option>
                      <option>Harvesting</option>
                      <option>Soil Testing</option>
                      <option>Maintenance</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Field/Location</label>
                    <select className="w-full border border-gray-300 rounded-lg px-3 py-2">
                      <option>Field A - Maize Section</option>
                      <option>Field B - Cassava Section</option>
                      <option>Field C - Plantain Section</option>
                      <option>Field D - Cocoa Section</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Assignee</label>
                    <select className="w-full border border-gray-300 rounded-lg px-3 py-2">
                      <option>Paul Tabi</option>
                      <option>Marie Atangana</option>
                      <option>Ibrahim Moussa</option>
                      <option>Ngozi Mbah</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                    <select className="w-full border border-gray-300 rounded-lg px-3 py-2">
                      <option>High</option>
                      <option>Medium</option>
                      <option>Low</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Scheduled Date</label>
                    <input type="datetime-local" className="w-full border border-gray-300 rounded-lg px-3 py-2" />
                  </div>
                </div>
              </div>
              <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
                <button onClick={() => setIsActivityModalOpen(false)} className="btn-outline">Cancel</button>
                <button
                  onClick={() => {
                    alert('Activity scheduled successfully!')
                    setIsActivityModalOpen(false)
                  }}
                  className="btn-primary"
                >
                  Schedule Activity
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Schedule Report Modal */}
        {isScheduleModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-md w-full">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-bold text-gray-900">Schedule Report</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Report Type</label>
                    <select className="w-full border border-gray-300 rounded-lg px-3 py-2">
                      <option>Financial Report</option>
                      <option>Activity Report</option>
                      <option>Inventory Report</option>
                      <option>Crop Report</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Frequency</label>
                    <select className="w-full border border-gray-300 rounded-lg px-3 py-2">
                      <option>Weekly</option>
                      <option>Monthly</option>
                      <option>Quarterly</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                    <input type="date" className="w-full border border-gray-300 rounded-lg px-3 py-2" />
                  </div>
                </div>
              </div>
              <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
                <button onClick={() => setIsScheduleModalOpen(false)} className="btn-outline">Cancel</button>
                <button
                  onClick={() => {
                    alert('Report scheduled successfully!')
                    setIsScheduleModalOpen(false)
                  }}
                  className="btn-primary"
                >
                  Schedule Report
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Transaction Modal */}
        {isTransactionModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-md w-full">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-bold text-gray-900">Add Transaction</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Transaction Type</label>
                    <select className="w-full border border-gray-300 rounded-lg px-3 py-2">
                      <option>Income</option>
                      <option>Expense</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                    <select className="w-full border border-gray-300 rounded-lg px-3 py-2">
                      <option>Crop Sales</option>
                      <option>Equipment</option>
                      <option>Labor</option>
                      <option>Fertilizer</option>
                      <option>Other</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Amount (CFA)</label>
                    <input type="number" placeholder="0" className="w-full border border-gray-300 rounded-lg px-3 py-2" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea placeholder="Transaction description..." className="w-full border border-gray-300 rounded-lg px-3 py-2 h-20"></textarea>
                  </div>
                </div>
              </div>
              <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
                <button onClick={() => setIsTransactionModalOpen(false)} className="btn-outline">Cancel</button>
                <button
                  onClick={() => {
                    alert('Transaction added successfully!')
                    setIsTransactionModalOpen(false)
                  }}
                  className="btn-primary"
                >
                  Add Transaction
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Report Modal */}
        {isReportModalOpen && selectedReport && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-gray-900">{selectedReport.title}</h2>
                  <button
                    onClick={() => setIsReportModalOpen(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ✕
                  </button>
                </div>
                <p className="text-sm text-gray-600 mt-1">{farm.name} - {selectedReport.period}</p>
              </div>

              <div className="p-6">
                {selectedReport.type === 'financial' && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-green-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-green-800">Total Revenue</h3>
                        <p className="text-2xl font-bold text-green-600">{formatCurrency(selectedReport.data.revenue)}</p>
                      </div>
                      <div className="bg-red-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-red-800">Total Expenses</h3>
                        <p className="text-2xl font-bold text-red-600">{formatCurrency(selectedReport.data.expenses)}</p>
                      </div>
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-blue-800">Net Profit</h3>
                        <p className="text-2xl font-bold text-blue-600">{formatCurrency(selectedReport.data.profit)}</p>
                      </div>
                    </div>
                    <div className="prose max-w-none">
                      <h3>Financial Summary</h3>
                      <p>This report provides a comprehensive overview of the farm's financial performance for {selectedReport.period}.</p>
                      <ul>
                        <li>Revenue increased by 12% compared to previous month</li>
                        <li>Operating expenses remained within budget</li>
                        <li>Profit margin improved to 28%</li>
                        <li>Cash flow remains positive</li>
                      </ul>
                    </div>
                  </div>
                )}

                {selectedReport.type === 'activity' && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-blue-800">Total Activities</h3>
                        <p className="text-2xl font-bold text-blue-600">{selectedReport.data.totalActivities}</p>
                      </div>
                      <div className="bg-green-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-green-800">Completed</h3>
                        <p className="text-2xl font-bold text-green-600">{selectedReport.data.completedActivities}</p>
                      </div>
                      <div className="bg-yellow-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-yellow-800">Pending</h3>
                        <p className="text-2xl font-bold text-yellow-600">{selectedReport.data.pendingActivities}</p>
                      </div>
                    </div>
                    <div className="prose max-w-none">
                      <h3>Activity Summary</h3>
                      <p>Farm activity completion rate for {selectedReport.period}: 83%</p>
                      <ul>
                        <li>Irrigation activities: 100% completed</li>
                        <li>Planting activities: 95% completed</li>
                        <li>Fertilizer application: 90% completed</li>
                        <li>Pest control: 85% completed</li>
                      </ul>
                    </div>
                  </div>
                )}

                {selectedReport.type === 'inventory' && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div className="bg-purple-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-purple-800">Total Items</h3>
                        <p className="text-2xl font-bold text-purple-600">{selectedReport.data.totalItems}</p>
                      </div>
                      <div className="bg-green-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-green-800">In Stock</h3>
                        <p className="text-2xl font-bold text-green-600">{selectedReport.data.inStock}</p>
                      </div>
                      <div className="bg-yellow-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-yellow-800">Low Stock</h3>
                        <p className="text-2xl font-bold text-yellow-600">{selectedReport.data.lowStock}</p>
                      </div>
                      <div className="bg-red-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-red-800">Out of Stock</h3>
                        <p className="text-2xl font-bold text-red-600">{selectedReport.data.outOfStock}</p>
                      </div>
                    </div>
                    <div className="prose max-w-none">
                      <h3>Inventory Status</h3>
                      <p>Current inventory levels and stock management for {selectedReport.period}.</p>
                      <ul>
                        <li>Seeds inventory: Adequate levels maintained</li>
                        <li>Fertilizer stock: Reorder required for NPK</li>
                        <li>Equipment: All tools in good condition</li>
                        <li>Pesticides: Stock levels optimal</li>
                      </ul>
                    </div>
                  </div>
                )}

                {selectedReport.type === 'crop' && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-green-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-green-800">Active Crops</h3>
                        <p className="text-2xl font-bold text-green-600">{selectedReport.data.activeCrops}</p>
                      </div>
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-blue-800">Healthy Plants</h3>
                        <p className="text-2xl font-bold text-blue-600">{selectedReport.data.healthyPlants}%</p>
                      </div>
                      <div className="bg-orange-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-orange-800">Expected Yield</h3>
                        <p className="text-2xl font-bold text-orange-600">{selectedReport.data.expectedYield}</p>
                      </div>
                    </div>
                    <div className="prose max-w-none">
                      <h3>Crop Health Assessment</h3>
                      <p>Overall crop health and yield projections for {selectedReport.period}.</p>
                      <ul>
                        <li>Maize: Excellent growth, expected harvest in 6 weeks</li>
                        <li>Cassava: Good health, minor pest issues addressed</li>
                        <li>Plantain: Optimal conditions, above-average yield expected</li>
                        <li>Cocoa: Healthy development, market prices favorable</li>
                      </ul>
                    </div>
                  </div>
                )}
              </div>

              <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
                <button
                  onClick={() => setIsReportModalOpen(false)}
                  className="btn-outline"
                >
                  Close
                </button>
                <button className="btn-primary">
                  Download PDF
                </button>
                <button className="btn-outline">
                  Copy to Clipboard
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
