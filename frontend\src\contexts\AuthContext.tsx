import React, { createContext, useContext, useState, useEffect } from 'react'
import { securityService } from '../services/securityService'

interface User {
  id: string
  email: string
  name: string
  role: 'admin' | 'manager' | 'worker'
}

interface AuthContextType {
  user: User | null
  isAuthenticated: boolean
  login: (email: string, password: string, name?: string) => Promise<void>
  logout: () => void
  loading: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Check for stored auth token on app load
    const token = localStorage.getItem('auth_token')
    const storedName = localStorage.getItem('user_name')
    if (token) {
      // In a real app, you'd validate the token with your backend
      // For now, we'll simulate a logged-in user
      setUser({
        id: '1',
        email: '<EMAIL>',
        name: storedName || 'Demo User',
        role: 'admin'
      })
    }
    setLoading(false)
  }, [])

  const login = async (email: string, password: string, name?: string) => {
    setLoading(true)
    try {
      // Validate email format
      if (!securityService.validateEmail(email)) {
        throw new Error('Invalid email format')
      }

      // Check if account is locked
      if (securityService.isAccountLocked(email)) {
        throw new Error('Account is temporarily locked due to multiple failed login attempts')
      }

      // Check rate limiting
      if (!securityService.checkRateLimit('login', 5, 60)) {
        throw new Error('Too many login attempts. Please try again later.')
      }

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Enhanced validation with multiple failure scenarios
      if (password.length < 6) {
        securityService.recordLoginAttempt(email, false)
        throw new Error('Password must be at least 6 characters long')
      }

      // Simulate various login failure scenarios
      if (password === '123456' || password === 'password' || password === 'admin') {
        securityService.recordLoginAttempt(email, false)
        throw new Error('Password is too weak. Please use a stronger password.')
      }

      if (email.toLowerCase().includes('blocked') || email.toLowerCase().includes('banned')) {
        securityService.recordLoginAttempt(email, false)
        throw new Error('This account has been suspended. Please contact support.')
      }

      if (password === 'wrong' || password === 'incorrect') {
        securityService.recordLoginAttempt(email, false)
        throw new Error('Invalid email or password. Please check your credentials.')
      }

      const mockUser: User = {
        id: '1',
        email,
        name: name || 'Demo User',
        role: 'admin'
      }

      // Record successful login
      securityService.recordLoginAttempt(email, true)
      securityService.refreshSession()

      setUser(mockUser)
      localStorage.setItem('auth_token', 'demo_token')
      localStorage.setItem('user_name', name || 'Demo User')
      localStorage.setItem('session_start', Date.now().toString())
    } catch (error) {
      securityService.recordLoginAttempt(email, false)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const logout = () => {
    console.log('Logout function called')
    securityService.clearSecurityData()
    setUser(null)
    localStorage.removeItem('user_name')
    // Force a page reload to ensure clean state
    window.location.reload()
  }

  const value = {
    user,
    isAuthenticated: !!user,
    login,
    logout,
    loading
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
