import { useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-leaflet'
import { MapPin, Layers, Maximize2, Minimize2 } from 'lucide-react'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
})

interface FieldMapProps {
  fields: Array<{
    id: number
    name: string
    farmName: string
    size: string
    soilType: string
    cropType?: string
    status: string
    coordinates: {
      lat: number
      lng: number
    }
  }>
  selectedField?: number | null
  onFieldSelect?: (fieldId: number) => void
  height?: string
}

export default function FieldMap({ fields, selectedField, onFieldSelect, height = "500px" }: FieldMapProps) {
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [selectedLayer, setSelectedLayer] = useState('satellite')

  // Map layers configuration
  const mapLayers = [
    { 
      id: 'satellite', 
      name: 'Satellite', 
      icon: '🛰️',
      url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}'
    },
    { 
      id: 'terrain', 
      name: 'Terrain', 
      icon: '🏔️',
      url: 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png'
    },
    { 
      id: 'street', 
      name: 'Street', 
      icon: '🗺️',
      url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'
    }
  ]

  const handleFieldClick = (fieldId: number) => {
    if (onFieldSelect) {
      onFieldSelect(fieldId)
    }
  }

  // Calculate map center based on fields
  const getMapCenter = (): [number, number] => {
    if (fields.length === 0) return [39.8283, -98.5795] // Center of USA
    
    const avgLat = fields.reduce((sum, field) => sum + field.coordinates.lat, 0) / fields.length
    const avgLng = fields.reduce((sum, field) => sum + field.coordinates.lng, 0) / fields.length
    
    return [avgLat, avgLng]
  }

  // Generate field boundary polygon
  const generateFieldBoundary = (field: any): [number, number][] => {
    const baseLatitude = field.coordinates.lat
    const baseLongitude = field.coordinates.lng
    const offset = 0.001 // Small offset for field boundary
    
    return [
      [baseLatitude - offset, baseLongitude - offset],
      [baseLatitude - offset, baseLongitude + offset],
      [baseLatitude + offset, baseLongitude + offset],
      [baseLatitude + offset, baseLongitude - offset],
    ]
  }

  // Get field color based on status
  const getFieldColor = (status: string) => {
    switch (status) {
      case 'Active': return '#10b981' // green
      case 'Fallow': return '#f59e0b' // yellow
      case 'Preparation': return '#3b82f6' // blue
      default: return '#6b7280' // gray
    }
  }

  // Get crop type icon
  const getCropIcon = (cropType?: string) => {
    switch (cropType?.toLowerCase()) {
      case 'corn': return '🌽'
      case 'wheat': return '🌾'
      case 'cotton': return '🌿'
      case 'soybeans': return '🫘'
      default: return '🌱'
    }
  }

  const currentLayer = mapLayers.find(layer => layer.id === selectedLayer) || mapLayers[0]

  return (
    <div className={`relative bg-gray-100 rounded-lg overflow-hidden ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      {/* Map Controls */}
      <div className="absolute top-4 left-4 z-[1000] space-y-2">
        {/* Layer Selector */}
        <div className="bg-white rounded-lg shadow-md p-2">
          <div className="flex space-x-1">
            {mapLayers.map((layer) => (
              <button
                key={layer.id}
                onClick={() => setSelectedLayer(layer.id)}
                className={`px-3 py-1 text-xs rounded ${
                  selectedLayer === layer.id
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
                title={layer.name}
              >
                {layer.icon}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Fullscreen Toggle */}
      <div className="absolute top-4 right-4 z-[1000]">
        <button
          onClick={() => setIsFullscreen(!isFullscreen)}
          className="bg-white rounded-lg shadow-md p-2 text-gray-600 hover:text-gray-800"
        >
          {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
        </button>
      </div>

      {/* Leaflet Map */}
      <div 
        className="w-full relative"
        style={{ height: isFullscreen ? '100vh' : height }}
      >
        <MapContainer
          center={getMapCenter()}
          zoom={12}
          style={{ height: '100%', width: '100%' }}
          className="rounded-lg"
        >
          <TileLayer
            url={currentLayer.url}
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          />
          
          {/* Field Markers and Boundaries */}
          {fields.map((field) => (
            <div key={field.id}>
              {/* Field Boundary Polygon */}
              <Polygon
                positions={generateFieldBoundary(field)}
                pathOptions={{
                  color: getFieldColor(field.status),
                  fillColor: getFieldColor(field.status),
                  fillOpacity: selectedField === field.id ? 0.6 : 0.3,
                  weight: selectedField === field.id ? 3 : 2
                }}
                eventHandlers={{
                  click: () => handleFieldClick(field.id)
                }}
              >
                <Popup>
                  <div className="p-3">
                    <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                      {getCropIcon(field.cropType)} {field.name}
                    </h4>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p><strong>Farm:</strong> {field.farmName}</p>
                      <p><strong>Size:</strong> {field.size}</p>
                      <p><strong>Soil:</strong> {field.soilType}</p>
                      <p><strong>Crop:</strong> {field.cropType || 'None'}</p>
                      <p><strong>Status:</strong> 
                        <span className={`ml-1 px-2 py-0.5 rounded-full text-xs ${
                          field.status === 'Active' ? 'bg-green-100 text-green-800' :
                          field.status === 'Fallow' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-blue-100 text-blue-800'
                        }`}>
                          {field.status}
                        </span>
                      </p>
                      <p><strong>Coordinates:</strong> {field.coordinates.lat.toFixed(4)}, {field.coordinates.lng.toFixed(4)}</p>
                    </div>
                  </div>
                </Popup>
              </Polygon>

              {/* Field Center Marker */}
              <Marker
                position={[field.coordinates.lat, field.coordinates.lng]}
                eventHandlers={{
                  click: () => handleFieldClick(field.id)
                }}
              >
                <Popup>
                  <div className="p-2">
                    <h5 className="font-medium flex items-center">
                      {getCropIcon(field.cropType)} {field.name}
                    </h5>
                    <p className="text-sm text-gray-600">{field.farmName}</p>
                    <p className="text-sm text-gray-600">{field.size} • {field.status}</p>
                  </div>
                </Popup>
              </Marker>
            </div>
          ))}
        </MapContainer>
      </div>

      {/* Map Legend */}
      <div className="absolute bottom-4 left-4 z-[1000] bg-white rounded-lg shadow-md p-3">
        <h4 className="text-xs font-semibold text-gray-900 mb-2">Field Status</h4>
        <div className="space-y-1 text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded"></div>
            <span>Active Fields</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-yellow-500 rounded"></div>
            <span>Fallow Fields</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-500 rounded"></div>
            <span>Preparation</span>
          </div>
          <div className="flex items-center space-x-2">
            <MapPin className="h-3 w-3 text-gray-600" />
            <span>Field Center</span>
          </div>
        </div>
      </div>

      {/* Field Count */}
      <div className="absolute bottom-4 right-4 z-[1000] bg-white rounded-lg shadow-md p-2">
        <div className="text-xs text-gray-600">Total Fields</div>
        <div className="text-lg font-bold text-gray-900">{fields.length}</div>
      </div>
    </div>
  )
}
