import { useState, useEffect } from 'react'
import { CheckCircle, X } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'

export default function WelcomeToast() {
  const [isVisible, setIsVisible] = useState(false)
  const { user, isAuthenticated } = useAuth()

  useEffect(() => {
    if (isAuthenticated && user) {
      // Show welcome toast after a short delay
      const timer = setTimeout(() => {
        setIsVisible(true)
      }, 1000)

      // Auto-hide after 5 seconds
      const hideTimer = setTimeout(() => {
        setIsVisible(false)
      }, 6000)

      return () => {
        clearTimeout(timer)
        clearTimeout(hideTimer)
      }
    }
  }, [isAuthenticated, user])

  if (!isVisible) return null

  return (
    <div className="fixed top-4 right-4 z-50 max-w-sm w-full">
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-4 transform transition-all duration-300 ease-in-out">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <CheckCircle className="h-6 w-6 text-green-400" />
          </div>
          <div className="ml-3 w-0 flex-1">
            <p className="text-sm font-medium text-gray-900">
              Welcome, {user?.name}! 🌾
            </p>
            <p className="mt-1 text-sm text-gray-500">
              You're now logged into Farm App. Ready to manage your farm operations!
            </p>
          </div>
          <div className="ml-4 flex-shrink-0 flex">
            <button
              className="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              onClick={() => setIsVisible(false)}
            >
              <span className="sr-only">Close</span>
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
