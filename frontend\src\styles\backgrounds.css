/* Farm App Background Styles */

/* Dashboard - Farm Overview */
.dashboard-bg {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.3) 0%, rgba(59, 130, 246, 0.3) 100%),
              url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2322c55e' fill-opacity='0.2'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  background-attachment: fixed;
  min-height: 100vh;
}

/* Farms - Rolling Hills */
.farms-bg {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.25) 0%, rgba(101, 163, 13, 0.25) 100%),
              url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 40c20-20 40 0 60-20s40 0 60-20v80H0V40z' fill='%2365a30d' fill-opacity='0.15'/%3E%3Cpath d='M0 60c20-15 40 5 60-15s40 5 60-15v55H0V60z' fill='%2322c55e' fill-opacity='0.2'/%3E%3C/svg%3E");
  background-attachment: fixed;
  min-height: 100vh;
}

/* Fields - Crop Rows */
.fields-bg {
  background: linear-gradient(135deg, rgba(101, 163, 13, 0.1) 0%, rgba(34, 197, 94, 0.1) 100%),
              url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%2365a30d' fill-opacity='0.04'%3E%3Cpath d='M0 0h80v10H0zM0 20h80v10H0zM0 40h80v10H0zM0 60h80v10H0z'/%3E%3C/g%3E%3C/svg%3E");
  background-attachment: fixed;
  min-height: 100vh;
}

/* Crops - Growing Plants */
.crops-bg {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.12) 0%, rgba(22, 163, 74, 0.12) 100%),
              url("data:image/svg+xml,%3Csvg width='120' height='120' viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%2322c55e' fill-opacity='0.06'%3E%3Cpath d='M60 10c0 20-16 36-36 36s-36-16-36-36 16-36 36-36 36 16 36 36zM132 10c0 20-16 36-36 36s-36-16-36-36 16-36 36-36 36 16 36 36zM60 82c0 20-16 36-36 36s-36-16-36-36 16-36 36-36 36 16 36 36zM132 82c0 20-16 36-36 36s-36-16-36-36 16-36 36-36 36 16 36 36z'/%3E%3C/g%3E%3C/svg%3E");
  background-attachment: fixed;
  min-height: 100vh;
}

/* Activities - Calendar Pattern */
.activities-bg {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(34, 197, 94, 0.1) 100%),
              url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%233b82f6' fill-opacity='0.04'%3E%3Crect x='0' y='0' width='20' height='20'/%3E%3Crect x='40' y='0' width='20' height='20'/%3E%3Crect x='80' y='0' width='20' height='20'/%3E%3Crect x='20' y='20' width='20' height='20'/%3E%3Crect x='60' y='20' width='20' height='20'/%3E%3Crect x='0' y='40' width='20' height='20'/%3E%3Crect x='40' y='40' width='20' height='20'/%3E%3Crect x='80' y='40' width='20' height='20'/%3E%3Crect x='20' y='60' width='20' height='20'/%3E%3Crect x='60' y='60' width='20' height='20'/%3E%3Crect x='0' y='80' width='20' height='20'/%3E%3Crect x='40' y='80' width='20' height='20'/%3E%3Crect x='80' y='80' width='20' height='20'/%3E%3C/g%3E%3C/svg%3E");
  background-attachment: fixed;
  min-height: 100vh;
}

/* Inventory - Storage Boxes */
.inventory-bg {
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%),
              url("data:image/svg+xml,%3Csvg width='90' height='90' viewBox='0 0 90 90' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23a855f7' fill-opacity='0.04'%3E%3Crect x='5' y='5' width='20' height='20' rx='2'/%3E%3Crect x='35' y='5' width='20' height='20' rx='2'/%3E%3Crect x='65' y='5' width='20' height='20' rx='2'/%3E%3Crect x='5' y='35' width='20' height='20' rx='2'/%3E%3Crect x='35' y='35' width='20' height='20' rx='2'/%3E%3Crect x='65' y='35' width='20' height='20' rx='2'/%3E%3Crect x='5' y='65' width='20' height='20' rx='2'/%3E%3Crect x='35' y='65' width='20' height='20' rx='2'/%3E%3Crect x='65' y='65' width='20' height='20' rx='2'/%3E%3C/g%3E%3C/svg%3E");
  background-attachment: fixed;
  min-height: 100vh;
}

/* Finance - Money Pattern */
.finance-bg {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%),
              url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%2310b981' fill-opacity='0.05'%3E%3Ccircle cx='20' cy='20' r='8'/%3E%3Ccircle cx='60' cy='20' r='8'/%3E%3Ccircle cx='40' cy='40' r='8'/%3E%3Ccircle cx='20' cy='60' r='8'/%3E%3Ccircle cx='60' cy='60' r='8'/%3E%3C/g%3E%3C/svg%3E");
  background-attachment: fixed;
  min-height: 100vh;
}

/* Weather - Cloud Pattern */
.weather-bg {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.12) 0%, rgba(147, 197, 253, 0.12) 100%),
              url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%233b82f6' fill-opacity='0.06'%3E%3Cpath d='M25 30c0-8.284 6.716-15 15-15s15 6.716 15 15c5.523 0 10 4.477 10 10s-4.477 10-10 10H25c-5.523 0-10-4.477-10-10s4.477-10 10-10z'/%3E%3Cpath d='M75 70c0-8.284 6.716-15 15-15s15 6.716 15 15c5.523 0 10 4.477 10 10s-4.477 10-10 10H75c-5.523 0-10-4.477-10-10s4.477-10 10-10z' transform='translate(-50 0)'/%3E%3C/g%3E%3C/svg%3E");
  background-attachment: fixed;
  min-height: 100vh;
}

/* Reports - Chart Pattern */
.reports-bg {
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.1) 0%, rgba(236, 72, 153, 0.1) 100%),
              url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23a855f7' fill-opacity='0.04'%3E%3Crect x='10' y='60' width='8' height='30'/%3E%3Crect x='25' y='40' width='8' height='50'/%3E%3Crect x='40' y='20' width='8' height='70'/%3E%3Crect x='55' y='50' width='8' height='40'/%3E%3Crect x='70' y='30' width='8' height='60'/%3E%3C/g%3E%3C/svg%3E");
  background-attachment: fixed;
  min-height: 100vh;
}

/* Security - Shield Pattern */
.security-bg {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(245, 101, 101, 0.1) 100%),
              url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ef4444' fill-opacity='0.05'%3E%3Cpath d='M40 10l8 8v12l-8 8-8-8V18l8-8zM40 50l8 8v12l-8 8-8-8V58l8-8z'/%3E%3Cpath d='M10 40l8-8h12l8 8-8 8H18l-8-8zM50 40l8-8h12l8 8-8 8H58l-8-8z'/%3E%3C/g%3E%3C/svg%3E");
  background-attachment: fixed;
  min-height: 100vh;
}

/* Login - Welcome Pattern */
.login-bg {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.3) 0%, rgba(59, 130, 246, 0.3) 50%, rgba(168, 85, 247, 0.3) 100%),
              url("data:image/svg+xml,%3Csvg width='120' height='120' viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%2322c55e' fill-opacity='0.15'%3E%3Cpath d='M60 60m-30 0a30 30 0 1 1 60 0a30 30 0 1 1 -60 0'/%3E%3Cpath d='M60 60m-20 0a20 20 0 1 1 40 0a20 20 0 1 1 -40 0'/%3E%3Cpath d='M60 60m-10 0a10 10 0 1 1 20 0a10 10 0 1 1 -20 0'/%3E%3C/g%3E%3C/svg%3E");
  background-attachment: fixed;
  min-height: 100vh;
}

/* Subtle overlay for better text readability */
.page-overlay {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(0.5px);
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Card overlay for content sections */
.card-overlay {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(1px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Responsive background adjustments */
@media (max-width: 768px) {
  .dashboard-bg,
  .farms-bg,
  .fields-bg,
  .crops-bg,
  .activities-bg,
  .inventory-bg,
  .finance-bg,
  .weather-bg,
  .reports-bg,
  .security-bg,
  .login-bg {
    background-attachment: scroll;
  }
}

/* Animation for background elements */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.floating-element {
  animation: float 6s ease-in-out infinite;
}

/* Gradient overlays for depth */
.gradient-overlay-top {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100px;
  pointer-events: none;
}

.gradient-overlay-bottom {
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px;
  pointer-events: none;
}
