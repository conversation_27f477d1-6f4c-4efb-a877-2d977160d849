import { Routes, Route, Navigate } from 'react-router-dom'
import Layout from './components/Layout'
import Dashboard from './pages/Dashboard'
import Farms from './pages/Farms'
import FarmDetail from './pages/FarmDetail'
import Security from './pages/Security'
import Login from './pages/Login'
import WelcomeToast from './components/WelcomeToast'
import { AuthProvider, useAuth } from './contexts/AuthContext'

function AppRoutes() {
  const { isAuthenticated, logout } = useAuth()

  // Check for logout parameter in URL
  const urlParams = new URLSearchParams(window.location.search)
  if (urlParams.get('logout') === 'true') {
    logout()
    return <Login />
  }

  if (!isAuthenticated) {
    return <Login />
  }

  return (
    <Layout>
      <Routes>
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/farms" element={<Farms />} />
        <Route path="/farms/:farmId" element={<FarmDetail />} />
        <Route path="/security" element={<Security />} />
      </Routes>
    </Layout>
  )
}

function App() {
  return (
    <AuthProvider>
      <div className="min-h-screen bg-gray-50">
        <AppRoutes />
        <WelcomeToast />
      </div>
    </AuthProvider>
  )
}

export default App
