import api from './api'

export interface Field {
  id: number
  name: string
  farmId: number
  farmName: string
  size: string
  soilType: string
  cropType?: string
  status: 'Active' | 'Fallow' | 'Preparation'
  coordinates?: {
    lat: number
    lng: number
  }
  createdAt: string
  updatedAt: string
}

export interface CreateFieldData {
  name: string
  farmId: number
  size: string
  soilType: string
  cropType?: string
  status: 'Active' | 'Fallow' | 'Preparation'
}

export interface UpdateFieldData {
  name?: string
  farmId?: number
  size?: string
  soilType?: string
  cropType?: string
  status?: 'Active' | 'Fallow' | 'Preparation'
}

// Mock data for demo purposes
let mockFields: Field[] = [
  {
    id: 1,
    name: 'North Field A',
    farmId: 1,
    farmName: 'Green Valley Farm',
    size: '25 acres',
    soilType: 'Clay Loam',
    cropType: 'Corn',
    status: 'Active',
    coordinates: { lat: 40.7128, lng: -74.0060 },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 2,
    name: 'South Field B',
    farmId: 1,
    farmName: 'Green Valley Farm',
    size: '30 acres',
    soilType: 'Sandy Loam',
    cropType: 'Wheat',
    status: 'Active',
    coordinates: { lat: 40.7589, lng: -73.9851 },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 3,
    name: 'East Field C',
    farmId: 2,
    farmName: 'Sunrise Agriculture',
    size: '20 acres',
    soilType: 'Silt Loam',
    cropType: 'Cotton',
    status: 'Preparation',
    coordinates: { lat: 40.6892, lng: -74.0445 },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
]

export const fieldService = {
  // Get all fields
  async getFields(): Promise<Field[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))
    return [...mockFields]
  },

  // Get field by ID
  async getField(id: number): Promise<Field> {
    await new Promise(resolve => setTimeout(resolve, 300))
    const field = mockFields.find(f => f.id === id)
    if (!field) throw new Error('Field not found')
    return field
  },

  // Create new field
  async createField(data: CreateFieldData): Promise<Field> {
    await new Promise(resolve => setTimeout(resolve, 800))
    const newField: Field = {
      id: Math.max(...mockFields.map(f => f.id)) + 1,
      ...data,
      farmName: 'Green Valley Farm', // Mock farm name
      coordinates: { lat: 40.7128, lng: -74.0060 },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    mockFields.push(newField)
    return newField
  },

  // Update field
  async updateField(id: number, data: UpdateFieldData): Promise<Field> {
    await new Promise(resolve => setTimeout(resolve, 800))
    const index = mockFields.findIndex(f => f.id === id)
    if (index === -1) throw new Error('Field not found')

    mockFields[index] = {
      ...mockFields[index],
      ...data,
      updatedAt: new Date().toISOString()
    }
    return mockFields[index]
  },

  // Delete field
  async deleteField(id: number): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 500))
    const index = mockFields.findIndex(f => f.id === id)
    if (index === -1) throw new Error('Field not found')
    mockFields.splice(index, 1)
  },
}
