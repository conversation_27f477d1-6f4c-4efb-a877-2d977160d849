import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { X } from 'lucide-react'
import { Field, CreateFieldData, UpdateFieldData } from '../services/fieldService'

interface FieldModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: CreateFieldData | UpdateFieldData) => Promise<void>
  field?: Field | null
  loading?: boolean
}

export default function FieldModal({ isOpen, onClose, onSubmit, field, loading }: FieldModalProps) {
  const { register, handleSubmit, formState: { errors }, reset } = useForm<CreateFieldData>()
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    if (field) {
      reset({
        name: field.name,
        farmId: field.farmId,
        size: field.size,
        soilType: field.soilType,
        cropType: field.cropType || '',
        status: field.status,
      })
    } else {
      reset({
        name: '',
        farmId: 1,
        size: '',
        soilType: '',
        cropType: '',
        status: 'Active',
      })
    }
  }, [field, reset])

  const handleFormSubmit = async (data: CreateFieldData) => {
    setIsSubmitting(true)
    try {
      await onSubmit(data)
      onClose()
      reset()
    } catch (error) {
      console.error('Error submitting field:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={onClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full">
          <div className="flex items-center justify-between p-6 border-b">
            <h3 className="text-lg font-medium text-gray-900">
              {field ? 'Edit Field' : 'Add New Field'}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(handleFormSubmit)} className="p-6 space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Field Name *
              </label>
              <input
                {...register('name', { required: 'Field name is required' })}
                type="text"
                className="input mt-1"
                placeholder="Enter field name"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="farmId" className="block text-sm font-medium text-gray-700">
                Farm *
              </label>
              <select
                {...register('farmId', { required: 'Farm is required' })}
                className="input mt-1"
              >
                <option value={1}>Green Valley Farm</option>
                <option value={2}>Sunrise Agriculture</option>
                <option value={3}>Mountain View Ranch</option>
              </select>
              {errors.farmId && (
                <p className="mt-1 text-sm text-red-600">{errors.farmId.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="size" className="block text-sm font-medium text-gray-700">
                Size *
              </label>
              <input
                {...register('size', { required: 'Size is required' })}
                type="text"
                className="input mt-1"
                placeholder="e.g., 25 acres"
              />
              {errors.size && (
                <p className="mt-1 text-sm text-red-600">{errors.size.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="soilType" className="block text-sm font-medium text-gray-700">
                Soil Type *
              </label>
              <select
                {...register('soilType', { required: 'Soil type is required' })}
                className="input mt-1"
              >
                <option value="">Select soil type</option>
                <option value="Clay Loam">Clay Loam</option>
                <option value="Sandy Loam">Sandy Loam</option>
                <option value="Silt Loam">Silt Loam</option>
                <option value="Clay">Clay</option>
                <option value="Sand">Sand</option>
                <option value="Silt">Silt</option>
                <option value="Loam">Loam</option>
              </select>
              {errors.soilType && (
                <p className="mt-1 text-sm text-red-600">{errors.soilType.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="cropType" className="block text-sm font-medium text-gray-700">
                Current Crop
              </label>
              <input
                {...register('cropType')}
                type="text"
                className="input mt-1"
                placeholder="e.g., Corn, Wheat (optional)"
              />
            </div>

            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                Status
              </label>
              <select
                {...register('status')}
                className="input mt-1"
              >
                <option value="Active">Active</option>
                <option value="Fallow">Fallow</option>
                <option value="Preparation">Preparation</option>
              </select>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="btn-outline"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn-primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : field ? 'Update Field' : 'Add Field'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
