import { useState } from 'react'
import { Shield, Key, Users, AlertTriangle, CheckCircle, XCircle, Eye, EyeOff, Lock, UserCheck, Activity } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'

export default function Security() {
  const { user } = useAuth()
  const [showPassword, setShowPassword] = useState(false)
  const [currentPassword, setCurrentPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')

  const securityMetrics = [
    { name: 'Active Sessions', value: '3', icon: Users, status: 'normal' },
    { name: 'Failed Login Attempts', value: '2', icon: AlertTriangle, status: 'warning' },
    { name: 'Password Strength', value: 'Strong', icon: Key, status: 'good' },
    { name: 'Two-Factor Auth', value: 'Enabled', icon: Shield, status: 'good' },
  ]

  const recentSecurityEvents = [
    { id: 1, event: 'Successful login', location: 'Yaoundé, Cameroon', time: '2 hours ago', status: 'success' },
    { id: 2, event: 'Password changed', location: 'Douala, Cameroon', time: '1 day ago', status: 'info' },
    { id: 3, event: 'Failed login attempt', location: 'Unknown location', time: '2 days ago', status: 'warning' },
    { id: 4, event: 'Account locked', location: 'Bamenda, Cameroon', time: '3 days ago', status: 'error' },
  ]

  const handlePasswordChange = (e: React.FormEvent) => {
    e.preventDefault()
    if (newPassword !== confirmPassword) {
      alert('New passwords do not match')
      return
    }
    if (newPassword.length < 6) {
      alert('Password must be at least 6 characters long')
      return
    }
    alert('Password changed successfully!')
    setCurrentPassword('')
    setNewPassword('')
    setConfirmPassword('')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Security Center</h1>
            <p className="text-gray-600">Manage your account security and monitor access</p>
          </div>
          <Shield className="h-12 w-12 text-blue-500" />
        </div>
      </div>

      {/* Security Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {securityMetrics.map((metric) => (
          <div key={metric.name} className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className={`p-3 rounded-lg ${
                metric.status === 'good' ? 'bg-green-100' :
                metric.status === 'warning' ? 'bg-yellow-100' :
                'bg-gray-100'
              }`}>
                <metric.icon className={`h-6 w-6 ${
                  metric.status === 'good' ? 'text-green-600' :
                  metric.status === 'warning' ? 'text-yellow-600' :
                  'text-gray-600'
                }`} />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">{metric.name}</p>
                <p className="text-2xl font-bold text-gray-900">{metric.value}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Password Change */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center mb-6">
            <Key className="h-6 w-6 text-blue-500 mr-3" />
            <h2 className="text-lg font-semibold text-gray-900">Change Password</h2>
          </div>
          
          <form onSubmit={handlePasswordChange} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Current Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={currentPassword}
                  onChange={(e) => setCurrentPassword(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 pr-10"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-2.5 text-gray-400"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                New Password
              </label>
              <input
                type={showPassword ? 'text' : 'password'}
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Confirm New Password
              </label>
              <input
                type={showPassword ? 'text' : 'password'}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2"
                required
              />
            </div>
            
            <button type="submit" className="btn-primary w-full">
              Change Password
            </button>
          </form>
        </div>

        {/* Account Security */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center mb-6">
            <UserCheck className="h-6 w-6 text-green-500 mr-3" />
            <h2 className="text-lg font-semibold text-gray-900">Account Security</h2>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <Lock className="h-5 w-5 text-gray-500 mr-3" />
                <div>
                  <p className="font-medium text-gray-900">Two-Factor Authentication</p>
                  <p className="text-sm text-gray-600">Add an extra layer of security</p>
                </div>
              </div>
              <button className="btn-outline text-sm">Enable</button>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <Activity className="h-5 w-5 text-gray-500 mr-3" />
                <div>
                  <p className="font-medium text-gray-900">Login Notifications</p>
                  <p className="text-sm text-gray-600">Get notified of new logins</p>
                </div>
              </div>
              <button className="btn-primary text-sm">Enabled</button>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <Shield className="h-5 w-5 text-gray-500 mr-3" />
                <div>
                  <p className="font-medium text-gray-900">Account Recovery</p>
                  <p className="text-sm text-gray-600">Set up recovery options</p>
                </div>
              </div>
              <button className="btn-outline text-sm">Setup</button>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Security Events */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center mb-6">
          <Activity className="h-6 w-6 text-purple-500 mr-3" />
          <h2 className="text-lg font-semibold text-gray-900">Recent Security Events</h2>
        </div>
        
        <div className="space-y-4">
          {recentSecurityEvents.map((event) => (
            <div key={event.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center">
                <div className={`p-2 rounded-full mr-4 ${
                  event.status === 'success' ? 'bg-green-100' :
                  event.status === 'warning' ? 'bg-yellow-100' :
                  event.status === 'error' ? 'bg-red-100' :
                  'bg-blue-100'
                }`}>
                  {event.status === 'success' ? <CheckCircle className="h-4 w-4 text-green-600" /> :
                   event.status === 'warning' ? <AlertTriangle className="h-4 w-4 text-yellow-600" /> :
                   event.status === 'error' ? <XCircle className="h-4 w-4 text-red-600" /> :
                   <Shield className="h-4 w-4 text-blue-600" />}
                </div>
                <div>
                  <p className="font-medium text-gray-900">{event.event}</p>
                  <p className="text-sm text-gray-600">{event.location}</p>
                </div>
              </div>
              <span className="text-sm text-gray-500">{event.time}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
