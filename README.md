# 🌾 Farm App - Comprehensive Farm Management System

A modern, responsive farm management application built for Cameroon's agricultural sector. Manage multiple farms, track crops, schedule activities, monitor finances, and generate detailed reports.

## ✨ Features

### 🏠 **Dashboard**
- Overview of all farms across Cameroon
- Real-time statistics and metrics
- Recent activities tracking
- Quick access to farm management features

### 🌾 **Farm Management**
- **5 Cameroon Farms**: Bamenda, Kribi, Maroua, Bafoussam, Bertoua
- Individual farm detail pages with comprehensive management
- Real GPS coordinates and Google Maps integration
- Farm-specific analytics and reporting

### 📊 **Comprehensive Features**
- **Crop Management**: Add, track, and monitor crop health
- **Activity Scheduling**: Plan and track farm activities
- **Financial Analytics**: Revenue, expenses, and profit tracking
- **Weather Monitoring**: Current conditions and forecasts
- **Inventory Management**: Track supplies and equipment
- **Report Generation**: Automated and scheduled reports
- **Map Integration**: Google Maps with satellite view

### 🔒 **Security**
- Secure authentication with multiple failure scenarios
- Account security management
- Session monitoring and activity logs
- Password strength requirements

## 🛠️ Technology Stack

### **Frontend**
- **React 18** with TypeScript
- **Tailwind CSS** for responsive design
- **Chart.js** for data visualization
- **Leaflet** for interactive maps
- **React Router** for navigation
- **Lucide React** for icons

### **Backend**
- **Node.js** with Express
- **TypeScript** for type safety
- **MongoDB** with Mongoose ODM
- **JWT** authentication
- **CORS** enabled for cross-origin requests

## 🚀 Getting Started

### **Prerequisites**
- Node.js 18+
- MongoDB 6.0+
- npm or yarn

### **Installation**

1. **Clone the repository**
```bash
git clone https://github.com/yourusername/farm-app.git
cd farm-app
```

2. **Install dependencies**
```bash
# Install frontend dependencies
cd frontend
npm install

# Install backend dependencies
cd ../backend
npm install
```

3. **Setup MongoDB**
```bash
# Start MongoDB service (varies by OS)
# Windows: net start MongoDB
# macOS: brew services start mongodb-community
# Linux: sudo systemctl start mongod
```

4. **Configure environment variables**
```bash
# Copy environment template
cd backend
cp .env.example .env

# Edit .env file with your MongoDB connection string
MONGODB_URI=mongodb://localhost:27017/farm-app
```

5. **Seed the database**
```bash
cd backend
npm run db:seed
```

6. **Start the development servers**

**Backend (Terminal 1):**
```bash
cd backend
npm run dev
```

**Frontend (Terminal 2):**
```bash
cd frontend
npm run dev
```

7. **Open the application**
- Frontend: http://localhost:5175
- Backend API: http://localhost:3001

## 📱 Responsive Design

The application is fully responsive and optimized for:
- **Desktop** (1024px+)
- **Tablet** (768px - 1023px)
- **Mobile** (320px - 767px)

## 🇨🇲 Cameroon Context

### **Authentic Farm Locations**
- **Ferme Agricole Bamenda** - North West Region
- **Plantation Kribi** - South Region
- **Ferme Maroua** - Far North Region
- **Agro-Ferme Bafoussam** - West Region
- **Plantation Bertoua** - East Region

### **Local Features**
- **CFA Franc** currency throughout
- **Local crops**: Maize, Cassava, Plantain, Cocoa, etc.
- **Real GPS coordinates** for each region
- **French language** elements where appropriate

## 🔐 Security Testing

Test login failures with these scenarios:
- **Weak passwords**: "123456", "password", "admin"
- **Invalid credentials**: "wrong", "incorrect"
- **Blocked accounts**: emails containing "blocked" or "banned"
- **Short passwords**: Less than 6 characters

## 🗄️ Database Schema

### **MongoDB Collections**
- **farms**: Farm information and details
- **users**: User authentication and profiles
- **activities**: Farm activities and tasks
- **crops**: Crop tracking and management

### **Key Features**
- **Mongoose ODM** for data modeling
- **Indexed queries** for performance
- **Data validation** and constraints
- **Relationship management** between collections

## 🌐 Deployment

### **MongoDB Atlas (Recommended)**
1. Create MongoDB Atlas account
2. Create cluster and database
3. Get connection string
4. Update MONGODB_URI in environment variables

### **Vercel Deployment**
The application is optimized for Vercel deployment with:
- Automatic builds from GitHub
- Environment variable support
- Serverless function compatibility

### **Environment Variables**
```bash
# Frontend
VITE_API_URL=http://localhost:3001

# Backend
MONGODB_URI=mongodb://localhost:27017/farm-app
JWT_SECRET=your-super-secret-jwt-key
PORT=3001
NODE_ENV=production
```

## 📝 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📞 Support

For support and questions, please open an issue on GitHub.

---

**Built with ❤️ for Cameroon's agricultural sector** 🇨🇲🌾
