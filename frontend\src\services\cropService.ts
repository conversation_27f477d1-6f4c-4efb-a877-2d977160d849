import api from './api'

export interface Crop {
  id: number
  name: string
  variety: string
  fieldId: number
  fieldName: string
  farmName: string
  plantingDate: string
  expectedHarvestDate: string
  actualHarvestDate?: string
  area: number
  unit: string
  growthStage: 'Planted' | 'Germination' | 'Vegetative' | 'Flowering' | 'Maturity' | 'Harvested'
  healthStatus: 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'Critical'
  expectedYield: number
  actualYield?: number
  yieldUnit: string
  notes?: string
  irrigationSchedule?: string
  fertilizationPlan?: string
  pestManagement?: string
  cost: number
  revenue?: number
  profit?: number
  createdAt: string
  updatedAt: string
}

export interface CreateCropData {
  name: string
  variety: string
  fieldId: number
  plantingDate: string
  expectedHarvestDate: string
  area: number
  unit: string
  growthStage: 'Planted' | 'Germination' | 'Vegetative' | 'Flowering' | 'Maturity' | 'Harvested'
  healthStatus: 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'Critical'
  expectedYield: number
  yieldUnit: string
  notes?: string
  irrigationSchedule?: string
  fertilizationPlan?: string
  pestManagement?: string
  cost: number
}

export interface UpdateCropData {
  name?: string
  variety?: string
  fieldId?: number
  plantingDate?: string
  expectedHarvestDate?: string
  actualHarvestDate?: string
  area?: number
  unit?: string
  growthStage?: 'Planted' | 'Germination' | 'Vegetative' | 'Flowering' | 'Maturity' | 'Harvested'
  healthStatus?: 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'Critical'
  expectedYield?: number
  actualYield?: number
  yieldUnit?: string
  notes?: string
  irrigationSchedule?: string
  fertilizationPlan?: string
  pestManagement?: string
  cost?: number
  revenue?: number
}

// Mock data for demo purposes
let mockCrops: Crop[] = [
  {
    id: 1,
    name: 'Corn',
    variety: 'Sweet Corn Hybrid',
    fieldId: 1,
    fieldName: 'North Field A',
    farmName: 'Green Valley Farm',
    plantingDate: '2024-04-15',
    expectedHarvestDate: '2024-09-15',
    area: 25,
    unit: 'acres',
    growthStage: 'Flowering',
    healthStatus: 'Excellent',
    expectedYield: 180,
    actualYield: undefined,
    yieldUnit: 'bushels/acre',
    notes: 'Excellent growth conditions this season. Regular irrigation maintained.',
    irrigationSchedule: 'Every 3 days, 2 inches',
    fertilizationPlan: 'NPK 20-10-10 applied monthly',
    pestManagement: 'Integrated pest management with organic pesticides',
    cost: 7500000, // 12,500 USD = ~7.5M CFA
    revenue: undefined,
    profit: undefined,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 2,
    name: 'Wheat',
    variety: 'Winter Wheat',
    fieldId: 2,
    fieldName: 'South Field B',
    farmName: 'Green Valley Farm',
    plantingDate: '2023-10-20',
    expectedHarvestDate: '2024-07-20',
    actualHarvestDate: '2024-07-18',
    area: 30,
    unit: 'acres',
    growthStage: 'Harvested',
    healthStatus: 'Good',
    expectedYield: 45,
    actualYield: 48,
    yieldUnit: 'bushels/acre',
    notes: 'Successful harvest with yield exceeding expectations.',
    irrigationSchedule: 'Rainfall dependent',
    fertilizationPlan: 'Organic compost and nitrogen supplement',
    pestManagement: 'Crop rotation and beneficial insects',
    cost: 5100000, // 8,500 USD = ~5.1M CFA
    revenue: 12960000, // 21,600 USD = ~13M CFA
    profit: 7860000, // 13,100 USD = ~7.9M CFA
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 3,
    name: 'Cotton',
    variety: 'Upland Cotton',
    fieldId: 3,
    fieldName: 'East Field C',
    farmName: 'Sunrise Agriculture',
    plantingDate: '2024-05-01',
    expectedHarvestDate: '2024-10-15',
    area: 20,
    unit: 'acres',
    growthStage: 'Vegetative',
    healthStatus: 'Fair',
    expectedYield: 800,
    actualYield: undefined,
    yieldUnit: 'lbs/acre',
    notes: 'Some pest pressure detected. Monitoring closely.',
    irrigationSchedule: 'Drip irrigation, daily',
    fertilizationPlan: 'Balanced fertilizer program',
    pestManagement: 'Weekly monitoring, targeted treatments',
    cost: 9000000, // 15,000 USD = ~9M CFA
    revenue: undefined,
    profit: undefined,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 4,
    name: 'Soybeans',
    variety: 'Non-GMO Soybeans',
    fieldId: 1,
    fieldName: 'North Field A',
    farmName: 'Green Valley Farm',
    plantingDate: '2024-05-20',
    expectedHarvestDate: '2024-10-01',
    area: 15,
    unit: 'acres',
    growthStage: 'Maturity',
    healthStatus: 'Good',
    expectedYield: 50,
    actualYield: undefined,
    yieldUnit: 'bushels/acre',
    notes: 'Ready for harvest soon. Good pod development.',
    irrigationSchedule: 'As needed basis',
    fertilizationPlan: 'Minimal - nitrogen fixing crop',
    pestManagement: 'Organic approved treatments only',
    cost: 3900000, // 6,500 USD = ~3.9M CFA
    revenue: undefined,
    profit: undefined,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
]

export const cropService = {
  // Get all crops
  async getCrops(): Promise<Crop[]> {
    await new Promise(resolve => setTimeout(resolve, 500))
    return [...mockCrops]
  },

  // Get crop by ID
  async getCrop(id: number): Promise<Crop> {
    await new Promise(resolve => setTimeout(resolve, 300))
    const crop = mockCrops.find(c => c.id === id)
    if (!crop) throw new Error('Crop not found')
    return crop
  },

  // Create new crop
  async createCrop(data: CreateCropData): Promise<Crop> {
    await new Promise(resolve => setTimeout(resolve, 800))
    const newCrop: Crop = {
      id: Math.max(...mockCrops.map(c => c.id)) + 1,
      ...data,
      fieldName: 'Field Name', // Mock field name
      farmName: 'Farm Name', // Mock farm name
      actualYield: undefined,
      revenue: undefined,
      profit: undefined,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    mockCrops.push(newCrop)
    return newCrop
  },

  // Update crop
  async updateCrop(id: number, data: UpdateCropData): Promise<Crop> {
    await new Promise(resolve => setTimeout(resolve, 800))
    const index = mockCrops.findIndex(c => c.id === id)
    if (index === -1) throw new Error('Crop not found')

    mockCrops[index] = {
      ...mockCrops[index],
      ...data,
      updatedAt: new Date().toISOString()
    }
    return mockCrops[index]
  },

  // Delete crop
  async deleteCrop(id: number): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 500))
    const index = mockCrops.findIndex(c => c.id === id)
    if (index === -1) throw new Error('Crop not found')
    mockCrops.splice(index, 1)
  },
}
