import { useState, useEffect } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-leaflet'
import { <PERSON>P<PERSON>, Layers, Maximize2, Minimize2 } from 'lucide-react'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
})

interface FarmLocation {
  id: number
  name: string
  coordinates: {
    latitude: number
    longitude: number
  }
  size: number
  sizeUnit: string
  farmType: string
  fields: FieldLocation[]
}

interface FieldLocation {
  id: number
  name: string
  coordinates: {
    latitude: number
    longitude: number
  }
  size: string
  cropType?: string
  status: string
}

interface FarmMapProps {
  farms: FarmLocation[]
  selectedFarm?: number | null
  onFarmSelect?: (farmId: number) => void
  height?: string
}

export default function FarmMap({ farms, selectedFarm, onFarmSelect, height = "400px" }: FarmMapProps) {
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [selectedLayer, setSelectedLayer] = useState('satellite')
  const [showFields, setShowFields] = useState(true)

  // Map layers configuration
  const mapLayers = [
    {
      id: 'satellite',
      name: 'Satellite',
      icon: '🛰️',
      url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}'
    },
    {
      id: 'terrain',
      name: 'Terrain',
      icon: '🏔️',
      url: 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png'
    },
    {
      id: 'street',
      name: 'Street',
      icon: '🗺️',
      url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'
    }
  ]

  const handleFarmClick = (farmId: number) => {
    if (onFarmSelect) {
      onFarmSelect(farmId)
    }
  }

  // Calculate map center based on farms
  const getMapCenter = (): [number, number] => {
    if (farms.length === 0) return [39.8283, -98.5795] // Center of USA

    const avgLat = farms.reduce((sum, farm) => sum + farm.coordinates.latitude, 0) / farms.length
    const avgLng = farms.reduce((sum, farm) => sum + farm.coordinates.longitude, 0) / farms.length

    return [avgLat, avgLng]
  }

  // Generate field boundaries for a farm
  const generateFieldBoundaries = (farm: FarmLocation) => {
    return farm.fields.map((field, index) => {
      const offset = 0.002 * (index + 1)
      return [
        [farm.coordinates.latitude + offset, farm.coordinates.longitude + offset],
        [farm.coordinates.latitude + offset, farm.coordinates.longitude + offset + 0.003],
        [farm.coordinates.latitude + offset + 0.002, farm.coordinates.longitude + offset + 0.003],
        [farm.coordinates.latitude + offset + 0.002, farm.coordinates.longitude + offset],
      ] as [number, number][]
    })
  }

  // Get field color based on status
  const getFieldColor = (status: string) => {
    switch (status) {
      case 'Active': return '#10b981' // green
      case 'Fallow': return '#f59e0b' // yellow
      case 'Preparation': return '#3b82f6' // blue
      default: return '#6b7280' // gray
    }
  }

  const currentLayer = mapLayers.find(layer => layer.id === selectedLayer) || mapLayers[0]

  return (
    <div className={`relative bg-gray-100 rounded-lg overflow-hidden ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      {/* Map Controls */}
      <div className="absolute top-4 left-4 z-10 space-y-2">
        {/* Layer Selector */}
        <div className="bg-white rounded-lg shadow-md p-2">
          <div className="flex space-x-1">
            {mapLayers.map((layer) => (
              <button
                key={layer.id}
                onClick={() => setSelectedLayer(layer.id)}
                className={`px-3 py-1 text-xs rounded ${
                  selectedLayer === layer.id
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
                title={layer.name}
              >
                {layer.icon}
              </button>
            ))}
          </div>
        </div>

        {/* Field Toggle */}
        <div className="bg-white rounded-lg shadow-md p-2">
          <button
            onClick={() => setShowFields(!showFields)}
            className={`flex items-center space-x-1 px-3 py-1 text-xs rounded ${
              showFields
                ? 'bg-green-600 text-white'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            <Layers className="h-3 w-3" />
            <span>Fields</span>
          </button>
        </div>
      </div>

      {/* Fullscreen Toggle */}
      <div className="absolute top-4 right-4 z-10">
        <button
          onClick={() => setIsFullscreen(!isFullscreen)}
          className="bg-white rounded-lg shadow-md p-2 text-gray-600 hover:text-gray-800"
        >
          {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
        </button>
      </div>

      {/* Leaflet Map */}
      <div
        className="w-full relative"
        style={{ height: isFullscreen ? '100vh' : height }}
      >
        <MapContainer
          center={getMapCenter()}
          zoom={10}
          style={{ height: '100%', width: '100%' }}
          className="rounded-lg"
        >
          <TileLayer
            url={currentLayer.url}
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          />

          {/* Farm Markers */}
          {farms.map((farm) => (
            <Marker
              key={farm.id}
              position={[farm.coordinates.latitude, farm.coordinates.longitude]}
              eventHandlers={{
                click: () => handleFarmClick(farm.id)
              }}
            >
              <Popup>
                <div className="p-2">
                  <h4 className="font-semibold text-gray-900 mb-2">{farm.name}</h4>
                  <div className="text-sm text-gray-600 space-y-1">
                    <p>📍 {farm.coordinates.latitude.toFixed(4)}, {farm.coordinates.longitude.toFixed(4)}</p>
                    <p>📏 {farm.size} {farm.sizeUnit}</p>
                    <p>🌾 {farm.farmType}</p>
                    <p>🏞️ {farm.fields.length} fields</p>
                  </div>
                </div>
              </Popup>
            </Marker>
          ))}

          {/* Field Boundaries */}
          {showFields && farms.map((farm) =>
            generateFieldBoundaries(farm).map((boundary, index) => (
              <Polygon
                key={`${farm.id}-field-${index}`}
                positions={boundary}
                pathOptions={{
                  color: getFieldColor(farm.fields[index]?.status || 'Active'),
                  fillColor: getFieldColor(farm.fields[index]?.status || 'Active'),
                  fillOpacity: 0.3,
                  weight: 2
                }}
              >
                <Popup>
                  <div className="p-2">
                    <h5 className="font-medium">{farm.fields[index]?.name || `Field ${index + 1}`}</h5>
                    <p className="text-sm text-gray-600">
                      Size: {farm.fields[index]?.size || 'Unknown'}
                    </p>
                    <p className="text-sm text-gray-600">
                      Crop: {farm.fields[index]?.cropType || 'None'}
                    </p>
                    <p className="text-sm text-gray-600">
                      Status: {farm.fields[index]?.status || 'Active'}
                    </p>
                  </div>
                </Popup>
              </Polygon>
            ))
          )}
        </MapContainer>

        {/* Map Legend */}
        <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-md p-3">
          <h4 className="text-xs font-semibold text-gray-900 mb-2">Legend</h4>
          <div className="space-y-1 text-xs">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-primary-600 rounded-full"></div>
              <span>Farm Location</span>
            </div>
            {showFields && (
              <>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-2 border border-green-500 bg-green-100"></div>
                  <span>Active Field</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-2 border border-yellow-500 bg-yellow-100"></div>
                  <span>Fallow Field</span>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Scale Indicator */}
        <div className="absolute bottom-4 right-4 bg-white rounded-lg shadow-md p-2">
          <div className="text-xs text-gray-600 mb-1">Scale</div>
          <div className="flex items-center space-x-1">
            <div className="w-8 h-0.5 bg-gray-900"></div>
            <span className="text-xs">1 km</span>
          </div>
        </div>
      </div>

      {/* Farm List (when fullscreen) */}
      {isFullscreen && (
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-10">
          <div className="bg-white rounded-lg shadow-lg p-4 max-w-md">
            <h3 className="font-semibold text-gray-900 mb-3">Farm Locations</h3>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {farms.map((farm) => (
                <button
                  key={farm.id}
                  onClick={() => handleFarmClick(farm.id)}
                  className={`w-full text-left px-3 py-2 rounded text-sm ${
                    selectedFarm === farm.id
                      ? 'bg-primary-100 text-primary-800 border border-primary-300'
                      : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <div className="font-medium">{farm.name}</div>
                  <div className="text-xs text-gray-500">
                    {farm.size} {farm.sizeUnit} • {farm.fields.length} fields
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
