import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-leaflet'
import { MapPin, Layers } from 'lucide-react'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
})

interface FarmDetailMapProps {
  farm: {
    id: number
    name: string
    coordinates: {
      latitude: number
      longitude: number
    }
    size: number
    sizeUnit: string
    address: string
  }
  height?: string
}

export default function FarmDetailMap({ farm, height = "300px" }: FarmDetailMapProps) {
  // Generate field boundaries around the farm
  const generateFieldBoundaries = () => {
    const baseLatitude = farm.coordinates.latitude
    const baseLongitude = farm.coordinates.longitude

    return [
      // Field A (top-left)
      [
        [baseLatitude + 0.002, baseLongitude - 0.002],
        [baseLatitude + 0.002, baseLongitude + 0.001],
        [baseLatitude + 0.004, baseLongitude + 0.001],
        [baseLatitude + 0.004, baseLongitude - 0.002],
      ],
      // Field B (top-right)
      [
        [baseLatitude + 0.002, baseLongitude + 0.001],
        [baseLatitude + 0.002, baseLongitude + 0.004],
        [baseLatitude + 0.004, baseLongitude + 0.004],
        [baseLatitude + 0.004, baseLongitude + 0.001],
      ],
      // Field C (bottom)
      [
        [baseLatitude - 0.002, baseLongitude - 0.001],
        [baseLatitude - 0.002, baseLongitude + 0.003],
        [baseLatitude + 0.001, baseLongitude + 0.003],
        [baseLatitude + 0.001, baseLongitude - 0.001],
      ],
    ] as [number, number][][]
  }

  const fieldBoundaries = generateFieldBoundaries()
  const fieldNames = ['Field A', 'Field B', 'Field C']
  const fieldColors = ['#10b981', '#f59e0b', '#3b82f6'] // green, yellow, blue

  return (
    <div className="relative bg-gray-100 rounded-lg overflow-hidden border">
      {/* Map Header */}
      <div className="absolute top-3 left-3 z-[1000] bg-white rounded-lg shadow-md px-3 py-2">
        <div className="flex items-center space-x-2">
          <MapPin className="h-4 w-4 text-primary-600" />
          <div>
            <div className="text-sm font-medium text-gray-900">{farm.name}</div>
            <div className="text-xs text-gray-500">
              {farm.coordinates.latitude.toFixed(4)}, {farm.coordinates.longitude.toFixed(4)}
            </div>
          </div>
        </div>
      </div>

      {/* Leaflet Map */}
      <div
        className="w-full relative"
        style={{ height }}
      >
        <MapContainer
          center={[farm.coordinates.latitude, farm.coordinates.longitude]}
          zoom={15}
          style={{ height: '100%', width: '100%' }}
          className="rounded-lg"
        >
          <TileLayer
            url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
            attribution='&copy; <a href="https://www.esri.com/">Esri</a>'
          />

          {/* Farm Center Marker */}
          <Marker position={[farm.coordinates.latitude, farm.coordinates.longitude]}>
            <Popup>
              <div className="p-2">
                <h4 className="font-semibold text-gray-900 mb-2">{farm.name}</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  <p>📍 {farm.address}</p>
                  <p>📏 {farm.size} {farm.sizeUnit}</p>
                  <p>🌾 Mixed Crops</p>
                </div>
              </div>
            </Popup>
          </Marker>

          {/* Field Boundaries */}
          {fieldBoundaries.map((boundary, index) => (
            <Polygon
              key={`field-${index}`}
              positions={boundary}
              pathOptions={{
                color: fieldColors[index],
                fillColor: fieldColors[index],
                fillOpacity: 0.3,
                weight: 2
              }}
            >
              <Popup>
                <div className="p-2">
                  <h5 className="font-medium">{fieldNames[index]}</h5>
                  <p className="text-sm text-gray-600">
                    Size: {Math.floor(farm.size / 3)} {farm.sizeUnit}
                  </p>
                  <p className="text-sm text-gray-600">
                    Status: {index === 0 ? 'Active' : index === 1 ? 'Fallow' : 'Irrigation'}
                  </p>
                </div>
              </Popup>
            </Polygon>
          ))}
        </MapContainer>
      </div>

      {/* Map Legend */}
      <div className="absolute bottom-3 left-3 z-[1000] bg-white rounded-lg shadow-md p-2">
        <div className="text-xs font-semibold text-gray-900 mb-1">Legend</div>
        <div className="space-y-1 text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-primary-600 rounded-full"></div>
            <span>Farm Center</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-2 bg-green-500"></div>
            <span>Active Field</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-2 bg-yellow-500"></div>
            <span>Fallow Field</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-2 bg-blue-500"></div>
            <span>Irrigation</span>
          </div>
        </div>
      </div>
    </div>
  )
}
