{"name": "farm-management-software", "version": "1.0.0", "description": "Comprehensive farm management software similar to Agrivi", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "cd backend && npm start", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["farm-management", "agriculture", "agrivi", "farming", "crop-management"], "author": "Farm Management Team", "license": "MIT", "dependencies": {"@types/crypto-js": "^4.2.2", "crypto-js": "^4.2.0"}}